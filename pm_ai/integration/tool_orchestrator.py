"""
Dynamic tool selection and execution orchestration.
Replaces hardcoded tool mapping with intelligent selection.
"""
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import logging
import time
import traceback

from ..intelligence.intent_parser import ParsedIntent, QueryIntent
from ..intelligence.llm_engine import LLMEngine

@dataclass
class ToolResult:
    """Result from tool execution."""
    tool_name: str
    success: bool
    data: Any
    error_message: Optional[str] = None
    execution_time: float = 0.0

class ToolOrchestrator:
    """
    Intelligent tool selection and execution orchestration.

    Determines which tools to use based on parsed intent and executes
    them with appropriate parameters extracted from the intent.
    """
    
    def __init__(self, llm_engine: Optional[LLMEngine] = None):
        self.llm_engine = llm_engine
        self.logger = logging.getLogger(__name__)
        
        # Register tools mapped to intents
        self.intent_to_tools = {
            QueryIntent.PROPERTY_PERFORMANCE: ["get_property_metrics"],
            QueryIntent.PROPERTY_COMPARISON: ["get_property_comparisons", "get_cohort_properties"],
            QueryIntent.RECOMMENDATIONS: ["get_property_recommendations"],
            QueryIntent.OUTLIER_ANALYSIS: ["get_outlier_statistics"],
            QueryIntent.COHORT_ANALYSIS: ["get_cohort_properties"],
            QueryIntent.GENERAL_INQUIRY: []
        }
    
    def select_tools(self, parsed_intent: ParsedIntent) -> List[str]:
        """
        Select appropriate tools based on parsed intent.
        
        Args:
            parsed_intent: Parsed user intent
            
        Returns:
            List of tool names to execute
        """
        # If confidence is too low, don't execute any tools
        if parsed_intent.confidence < 0.6:
            self.logger.warning(
                f"Intent confidence too low ({parsed_intent.confidence}) for tool execution"
            )
            return []
        
        # Get tools mapped to the intent
        return self.intent_to_tools.get(parsed_intent.intent, [])
    
    def execute_tools(
        self, 
        parsed_intent: ParsedIntent,
        available_tools: Dict[str, Callable],
    ) -> List[ToolResult]:
        """
        Execute the selected tools with parameters from parsed intent.
        
        Args:
            parsed_intent: Parsed user intent
            available_tools: Dictionary mapping tool names to callables
            
        Returns:
            List of ToolResult objects with execution results
        """
        # Select tools based on intent
        tool_names = self.select_tools(parsed_intent)
        if not tool_names:
            return []
        
        results = []
        for tool_name in tool_names:
            # Check if tool exists
            if tool_name not in available_tools:
                self.logger.warning(f"Tool '{tool_name}' not found")
                continue
            
            # Get tool function
            tool_fn = available_tools[tool_name]
            
            # Extract parameters for this tool
            params = self._extract_parameters_for_tool(tool_name, parsed_intent)
            
            # Execute tool
            start_time = time.time()
            try:
                data = tool_fn(**params)
                execution_time = time.time() - start_time
                
                results.append(ToolResult(
                    tool_name=tool_name,
                    success=True,
                    data=data,
                    execution_time=execution_time
                ))
                
                self.logger.info(f"Tool '{tool_name}' executed successfully in {execution_time:.2f}s")
                
            except Exception as e:
                execution_time = time.time() - start_time
                error_message = f"{type(e).__name__}: {str(e)}"
                
                results.append(ToolResult(
                    tool_name=tool_name,
                    success=False,
                    data=None,
                    error_message=error_message,
                    execution_time=execution_time
                ))
                
                self.logger.error(f"Tool '{tool_name}' execution failed: {error_message}")
                self.logger.debug(traceback.format_exc())
        
        return results
    
    def _extract_parameters_for_tool(self, tool_name: str, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """
        Extract parameters for a specific tool from parsed intent.
        
        Args:
            tool_name: Name of the tool
            parsed_intent: Parsed user intent
            
        Returns:
            Dictionary of parameters for the tool
        """
        intent_params = parsed_intent.parameters
        
        # Define parameter extraction based on tool name
        if tool_name == "get_property_metrics":
            return self._extract_metrics_parameters(parsed_intent)
        elif tool_name == "get_property_comparisons":
            return self._extract_comparison_parameters(parsed_intent)
        elif tool_name == "get_cohort_properties":
            return self._extract_cohort_parameters(parsed_intent)
        elif tool_name == "get_property_recommendations":
            return self._extract_recommendation_parameters(parsed_intent)
        elif tool_name == "get_outlier_statistics":
            return self._extract_outlier_parameters(parsed_intent)
        else:
            # Default: pass all parameters
            return intent_params
    
    def _extract_metrics_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for property metrics tool."""
        params = {}

        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        
        # Extract time period if specified
        if "time_period" in parsed_intent.parameters:
            params["time_period"] = parsed_intent.parameters["time_period"]
        
        # Extract specific metrics if requested
        if "metrics" in parsed_intent.parameters:
            params["metrics"] = parsed_intent.parameters["metrics"]
        
        return params
    
    def _extract_comparison_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for property comparison tool."""
        params = {}
        
        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        
        # Extract comparison type if specified
        if "comparison_type" in parsed_intent.parameters:
            params["comparison_type"] = parsed_intent.parameters["comparison_type"]
        
        # Extract time period if specified
        if "time_period" in parsed_intent.parameters:
            params["time_period"] = parsed_intent.parameters["time_period"]
        
        return params
    
    def _extract_cohort_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for cohort properties tool."""
        params = {}
        
        # Extract cohort name
        if "cohort" in parsed_intent.parameters:
            params["cohort_name"] = parsed_intent.parameters["cohort"]
        
        # Extract time period if specified
        if "time_period" in parsed_intent.parameters:
            params["time_period"] = parsed_intent.parameters["time_period"]
        
        return params
    
    def _extract_recommendation_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for property recommendations tool."""
        params = {}
        
        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        # Extract cohort if property ID not specified
        elif "cohort" in parsed_intent.parameters:
            params["cohort"] = parsed_intent.parameters["cohort"]
        
        return params
    
    def _extract_outlier_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for outlier statistics tool."""
        params = {}
        
        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        
        # Extract time period if specified
        if "time_period" in parsed_intent.parameters:
            params["time_period"] = parsed_intent.parameters["time_period"]
        
        # Extract specific metrics if requested
        if "metrics" in parsed_intent.parameters:
            params["metrics"] = parsed_intent.parameters["metrics"]
        
        return params
