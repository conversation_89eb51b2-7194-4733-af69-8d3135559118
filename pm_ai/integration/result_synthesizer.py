"""
Response generation and formatting for Property Manager AI.
Synthesizes tool results into coherent natural language responses.
"""
from typing import Dict, List, Optional, Any
import json

from ..intelligence.llm_engine import LLMEngine
from ..integration.tool_orchestrator import ToolResult

class ResultSynthesizer:
    """
    Synthesizes tool execution results into natural language responses.
    
    Replaces hardcoded response templates with dynamic LLM-generated
    responses that incorporate context and tool outputs.
    """
    
    def __init__(self, llm_engine: LLMEngine):
        self.llm_engine = llm_engine
    
    def synthesize_response(
        self,
        query: str,
        parsed_intent: Dict[str, Any],
        tool_results: List[ToolResult],
        conversation_context: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Generate a cohesive response from tool execution results.
        
        Args:
            query: Original user query
            parsed_intent: Parsed intent data
            tool_results: Results from tool executions
            conversation_context: Optional conversation context
            
        Returns:
            Formatted natural language response
        """
        # Handle case with no tool results
        if not tool_results:
            return self._generate_fallback_response(query, parsed_intent)
        
        # Check if all tools failed
        all_failed = all(not result.success for result in tool_results)
        if all_failed:
            return self._generate_error_response(query, tool_results)
        
        # Format tool results for LLM prompt
        formatted_results = self._format_tool_results(tool_results)
        
        # Build system prompt
        system_prompt = self._build_system_prompt()
        
        # Build user prompt
        user_prompt = self._build_user_prompt(
            query,
            parsed_intent,
            formatted_results,
            conversation_context
        )
        
        # Generate response with LLM
        messages = [
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ]
        
        response = self.llm_engine.create_completion(messages=messages)
        
        return response.get("content", "I couldn't generate a response at this time.")
    
    def _build_system_prompt(self) -> str:
        """Build system prompt for response generation."""
        return """You are an AI assistant for property managers. Your task is to generate clear, 
natural language responses based on the results from property analysis tools.

Follow these guidelines:
1. Be concise and professional in your responses
2. Focus on the specific data and insights provided by the tools
3. Format numeric values appropriately (currency with $ symbol, percentages with % symbol)
4. Highlight significant metrics or comparisons
5. Maintain a helpful, informative tone
6. Avoid making claims not supported by the provided data
7. When presenting recommendations, explain the reasoning behind them
8. If relevant, suggest other analyses that might be useful

The user is a property manager who wants insights about their vacation rental properties.
"""
    
    def _build_user_prompt(
        self,
        query: str,
        parsed_intent: Dict[str, Any],
        formatted_results: str,
        conversation_context: Optional[Dict[str, Any]]
    ) -> str:
        """Build user prompt for response generation."""
        prompt = f"User query: {query}\n\n"
        prompt += f"Intent: {parsed_intent.get('intent', 'unknown')}\n"
        prompt += f"Tools executed and their results:\n\n{formatted_results}\n\n"
        
        # Add conversation context if available
        if conversation_context and 'history' in conversation_context:
            recent_history = conversation_context['history'][-2:] if len(conversation_context['history']) > 2 else conversation_context['history']
            if recent_history:
                prompt += "Recent conversation context:\n"
                for turn in recent_history:
                    prompt += f"User: {turn.get('user', '')}\n"
                    prompt += f"System: {turn.get('system', '')}\n\n"
        
        prompt += "Please generate a helpful, natural language response that addresses the user's query based on the tool results."
        
        return prompt
    
    def _format_tool_results(self, tool_results: List[ToolResult]) -> str:
        """Format tool results for inclusion in prompt."""
        formatted = ""
        
        for i, result in enumerate(tool_results):
            formatted += f"Tool {i+1}: {result.tool_name}\n"
            formatted += f"Success: {result.success}\n"
            
            if result.success:
                if isinstance(result.data, dict):
                    # Pretty print dictionary data
                    formatted += "Data:\n"
                    formatted += json.dumps(result.data, indent=2)
                elif isinstance(result.data, str):
                    # String data
                    formatted += f"Data: {result.data}"
                else:
                    # Other data types
                    formatted += f"Data: {str(result.data)}"
            else:
                formatted += f"Error: {result.error_message}"
                
            formatted += "\n\n"
                
        return formatted
    
    def _generate_fallback_response(self, query: str, parsed_intent: Dict[str, Any]) -> str:
        """Generate fallback response when no tools were executed."""
        if parsed_intent.get("clarification_needed"):
            return parsed_intent["clarification_needed"]
            
        intent = parsed_intent.get("intent", "general_inquiry")
        
        if intent == "property_performance":
            return "I'd like to help you with property performance metrics, but I need more specific information. Could you specify which property or property cohort you're interested in?"
        elif intent == "property_comparison":
            return "I can help you compare properties, but I need to know which property or properties you'd like to analyze. Could you specify the property ID(s) or cohort?"
        elif intent == "recommendations":
            return "I'd be happy to provide recommendations, but I need more details. Which property or property type are you looking for recommendations about?"
        elif intent == "outlier_analysis":
            return "To provide outlier analysis, I need to know which property you're interested in. Could you specify a property ID?"
        elif intent == "cohort_analysis":
            return "I can provide cohort analysis, but I need to know which cohort you're interested in (beachfront, downtown, mountain_view, or luxury)."
        else:
            return "I'm not sure I understood what you're asking about. Could you provide more details about which property or metrics you're interested in?"
    
    def _generate_error_response(self, query: str, tool_results: List[ToolResult]) -> str:
        """Generate error response when tools failed."""
        errors = []
        for result in tool_results:
            if not result.success:
                errors.append(f"{result.tool_name}: {result.error_message}")
        
        error_details = "\n".join(errors)
        
        # Generate error response with LLM
        messages = [
            {"role": "system", "content": """You are an AI assistant handling property management queries.
Unfortunately, some tools have encountered errors. Craft a helpful response that:
1. Acknowledges the issue
2. Explains possible reasons (based on the error messages)
3. Suggests alternatives or workarounds if applicable
4. Maintains a professional, helpful tone"""},
            {"role": "user", "content": f"Query: {query}\n\nErrors encountered:\n{error_details}"}
        ]
        
        response = self.llm_engine.create_completion(messages=messages)
        
        return response.get("content", "I apologize, but I encountered an error while processing your request. Please try again with different parameters or a more specific query.")
