"""
Centralized configuration settings for the PM_AI application.
"""
import os
from pathlib import Path
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Base paths
PROJECT_ROOT = Path(__file__).parent.parent.parent
DATA_DIR = PROJECT_ROOT / "pm_ai" / "data"

# API keys
OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
EXCHANGERATE_API_KEY = os.getenv("EXCHANGERATE_API_KEY")

# Database
DB_PATH = os.getenv("DB_PATH", str(DATA_DIR / "transactions.db"))
