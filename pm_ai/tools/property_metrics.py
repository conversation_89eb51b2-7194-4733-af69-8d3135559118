"""
Tool for fetching property performance metrics.
"""
import json
import os
import sys
from typing import Dict, List, Optional, Any

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def get_property_metrics(property_id: str, period: Optional[str] = "last_30_days") -> Dict[str, Any]:
    """
    Fetch performance metrics for a specific property.

    Args:
        property_id: The ID of the property to fetch metrics for
        period: Time period for metrics (last_30_days, last_quarter, last_year)

    Returns:
        Dictionary containing performance metrics
    """
    # In a real implementation, this would query a database
    # For MVP, we'll use a static JSON file with sample data
    data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             "data", "property_metrics.json")
    
    try:
        with open(data_path, 'r') as f:
            all_properties = json.load(f)
        
        if property_id in all_properties:
            if period in all_properties[property_id]:
                return all_properties[property_id][period]
            else:
                return {"error": f"No data available for period: {period}"}
        else:
            return {"error": f"Property ID {property_id} not found"}
    
    except FileNotFoundError:
        return {
            "error": "Property metrics database not found. Please ensure property_metrics.json exists in the data directory."
        }
    except json.JSONDecodeError:
        return {
            "error": "Error reading property metrics data. The file may be corrupted."
        }
    except Exception as e:
        return {
            "error": f"Unexpected error: {str(e)}"
        }
