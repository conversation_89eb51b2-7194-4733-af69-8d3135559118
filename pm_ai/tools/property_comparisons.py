"""
Tool for comparing properties and generating insights.
"""
import json
import os
import sys
from typing import Dict, List, Optional, Any

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def get_property_comparisons(property_id: str, metric: Optional[str] = None) -> Dict[str, Any]:
    """
    Compare a property against similar properties and provide insights.

    Args:
        property_id: The ID of the property to compare
        metric: Optional specific metric to focus comparison on (occupancy_rate, revenue, etc.)
                If None, returns comparisons for all available metrics

    Returns:
        Dictionary containing comparison data and insights
    """
    # In a real implementation, this would query a database and run analytics
    # For MVP, we'll use static JSON files with sample data
    comparisons_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                   "data", "property_comparisons.json")
    
    try:
        with open(comparisons_path, 'r') as f:
            all_comparisons = json.load(f)
        
        if property_id in all_comparisons:
            property_data = all_comparisons[property_id]
            
            # If specific metric requested, filter the results
            if metric and metric in property_data:
                return {
                    "property_id": property_id,
                    "comparison_data": {
                        metric: property_data[metric]
                    }
                }
            elif metric:
                return {"error": f"Metric '{metric}' not found for property {property_id}"}
            else:
                return {
                    "property_id": property_id,
                    "comparison_data": property_data
                }
        else:
            return {"error": f"Comparison data not found for property ID {property_id}"}
    
    except FileNotFoundError:
        return {
            "error": "Property comparison data not found. Please ensure property_comparisons.json exists in the data directory."
        }
    except json.JSONDecodeError:
        return {
            "error": "Error reading property comparison data. The file may be corrupted."
        }
    except Exception as e:
        return {
            "error": f"Unexpected error: {str(e)}"
        }

def get_cohort_properties(cohort_id: str) -> Dict[str, Any]:
    """
    Get properties belonging to the same cohort for comparison.

    Args:
        cohort_id: The ID of the cohort to fetch properties for

    Returns:
        Dictionary containing properties in the cohort and summary statistics
    """
    # In a real implementation, this would query a database
    # For MVP, we'll use a static JSON file with sample data
    data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             "data", "property_cohorts.json")
    
    try:
        with open(data_path, 'r') as f:
            all_cohorts = json.load(f)
        
        if cohort_id in all_cohorts:
            return all_cohorts[cohort_id]
        else:
            return {"error": f"Cohort ID {cohort_id} not found"}
    
    except FileNotFoundError:
        return {
            "error": "Property cohort data not found. Please ensure property_cohorts.json exists in the data directory."
        }
    except json.JSONDecodeError:
        return {
            "error": "Error reading property cohort data. The file may be corrupted."
        }
    except Exception as e:
        return {
            "error": f"Unexpected error: {str(e)}"
        }
