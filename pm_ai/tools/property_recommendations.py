"""
Tool for generating recommendations based on property performance data.
"""
import json
import os
import sys
from typing import Dict, List, Optional, Any

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

def get_property_recommendations(property_id: str) -> Dict[str, Any]:
    """
    Generate recommendations for improving property performance.

    Args:
        property_id: The ID of the property to generate recommendations for

    Returns:
        Dictionary containing recommendations and supporting data
    """
    # In a real implementation, this would analyze performance data and generate recommendations
    # For MVP, we'll use static JSON files with pre-computed recommendations
    recommendations_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), 
                                      "data", "property_recommendations.json")
    
    try:
        with open(recommendations_path, 'r') as f:
            all_recommendations = json.load(f)
        
        if property_id in all_recommendations:
            return all_recommendations[property_id]
        else:
            return {"error": f"Recommendations not found for property ID {property_id}"}
    
    except FileNotFoundError:
        return {
            "error": "Property recommendations data not found. Please ensure property_recommendations.json exists in the data directory."
        }
    except json.JSONDecodeError:
        return {
            "error": "Error reading property recommendations data. The file may be corrupted."
        }
    except Exception as e:
        return {
            "error": f"Unexpected error: {str(e)}"
        }

def get_outlier_statistics(property_id: str) -> Dict[str, Any]:
    """
    Fetch outlier statistics for a specific property compared to similar properties.

    Args:
        property_id: The ID of the property to get outlier statistics for

    Returns:
        Dictionary containing outlier statistics and explanations
    """
    # In a real implementation, this would calculate outlier statistics from the database
    # For MVP, we'll use static JSON files with pre-computed outlier data
    data_path = os.path.join(os.path.dirname(os.path.dirname(os.path.abspath(__file__))), 
                             "data", "property_outliers.json")
    
    try:
        with open(data_path, 'r') as f:
            all_outliers = json.load(f)
        
        if property_id in all_outliers:
            return all_outliers[property_id]
        else:
            return {"error": f"Outlier statistics not found for property ID {property_id}"}
    
    except FileNotFoundError:
        return {
            "error": "Property outlier data not found. Please ensure property_outliers.json exists in the data directory."
        }
    except json.JSONDecodeError:
        return {
            "error": "Error reading property outlier data. The file may be corrupted."
        }
    except Exception as e:
        return {
            "error": f"Unexpected error: {str(e)}"
        }
