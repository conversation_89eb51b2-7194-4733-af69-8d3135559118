#!/usr/bin/env python
"""
PM_AI - Property Manager AI Assistant

This is the main entry point for the Property Manager AI application.
"""
import os
from dotenv import load_dotenv
import sys
import os

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pm_ai.agents import Runner
from pm_ai.agents.property_manager import agent

def main():
    """
    Run the Property Manager AI assistant with interactive prompts.
    """
    # Load environment variables
    load_dotenv()
    
    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY not found in environment variables.")
        print("Please add it to your .env file or export it directly.")
        return
    
    # Welcome message
    print("\n" + "="*60)
    print("Welcome to Property Manager AI - Your Vacation Rental Assistant")
    print("="*60)
    print("Ask me about property performance, comparisons, or recommendations.")
    print("Example questions:")
    print("- How is property P123 performing this month?")
    print("- Compare property P456 with similar properties.")
    print("- What are the recommended improvements for property P789?")
    print("- Show me the properties in the beachfront cohort.")
    print("- What are the outlier statistics for property P123?")
    print("Type 'exit' or 'quit' to end the session.")
    print("="*60 + "\n")
    
    # Interactive loop
    while True:
        # Get user input
        user_input = input("\nYou: ")
        
        # Check for exit command
        if user_input.lower() in ["exit", "quit"]:
            print("\nThank you for using Property Manager AI. Goodbye!")
            break
        
        # Process the query
        print("\nProperty Manager AI: ", end="")
        try:
            result = Runner.run_sync(agent, user_input)
            # Access the result as a dictionary, not an attribute
            print(result["final_output"])
        except Exception as e:
            print(f"Sorry, I encountered an error: {e}")
    
if __name__ == "__main__":
    main()
