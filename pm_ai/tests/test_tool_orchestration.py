"""
Tests for the tool orchestration and result synthesis components.
"""
import unittest
from unittest.mock import patch, MagicMock

from pm_ai.intelligence.intent_parser import ParsedIntent, QueryIntent
from pm_ai.integration.tool_orchestrator import ToolOrchestrator, ToolResult
from pm_ai.integration.result_synthesizer import ResultSynthesizer
from pm_ai.intelligence.llm_engine import LLMEngine

class TestToolOrchestrator(unittest.TestCase):
    """Test cases for the ToolOrchestrator."""

    def setUp(self):
        """Set up test environment."""
        self.mock_llm_engine = MagicMock(spec=LLMEngine)
        self.orchestrator = ToolOrchestrator(self.mock_llm_engine)
        
        # Set up mock tools
        self.mock_tools = {
            "get_property_metrics": MagicMock(return_value={
                "occupancy_rate": 0.85,
                "average_daily_rate": 150.25,
                "revenue": 12500
            }),
            "get_property_comparisons": MagicMock(return_value={
                "property_id": "P123",
                "cohort_avg_occupancy": 0.78,
                "difference": 0.07
            }),
            "get_property_recommendations": MagicMock(return_value=[
                "Increase pricing during peak season",
                "Improve property photos"
            ]),
            "get_outlier_statistics": MagicMock(return_value={
                "outliers": ["revenue", "occupancy_rate"],
                "percentile": 92
            })
        }
    
    def test_select_tools(self):
        """Test tool selection based on parsed intent."""
        # Test property performance intent
        performance_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"]}
        )
        
        tools = self.orchestrator.select_tools(performance_intent)
        self.assertIn("get_property_metrics", tools)
        
        # Test property comparison intent
        comparison_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.85,
            parameters={"property_ids": ["P123"]}
        )
        
        tools = self.orchestrator.select_tools(comparison_intent)
        self.assertIn("get_property_comparisons", tools)
        self.assertIn("get_cohort_properties", tools)
        
        # Test low confidence intent (should not select any tools)
        low_confidence_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.4,
            parameters={"property_ids": ["P123"]}
        )
        
        tools = self.orchestrator.select_tools(low_confidence_intent)
        self.assertEqual(len(tools), 0)
    
    def test_execute_tools(self):
        """Test execution of selected tools."""
        # Create intent for testing
        intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "time_period": "last_30_days"
            }
        )
        
        # Execute tools
        results = self.orchestrator.execute_tools(intent, self.mock_tools)
        
        # Verify results
        self.assertEqual(len(results), 1)
        self.assertTrue(results[0].success)
        self.assertEqual(results[0].tool_name, "get_property_metrics")
        
        # Verify tool was called with correct parameters
        self.mock_tools["get_property_metrics"].assert_called_once()
        args, kwargs = self.mock_tools["get_property_metrics"].call_args
        self.assertEqual(kwargs["property_id"], "P123")
        self.assertEqual(kwargs["time_period"], "last_30_days")
    
    def test_extract_parameters(self):
        """Test parameter extraction for tools."""
        # Test metrics parameters
        metrics_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={
                "property_ids": ["P123"],
                "time_period": "last_month",
                "metrics": ["occupancy", "revenue"]
            }
        )
        
        params = self.orchestrator._extract_metrics_parameters(metrics_intent)
        self.assertEqual(params["property_id"], "P123")
        self.assertEqual(params["time_period"], "last_month")
        self.assertEqual(params["metrics"], ["occupancy", "revenue"])
        
        # Test comparison parameters
        comparison_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_COMPARISON,
            confidence=0.9,
            parameters={
                "property_ids": ["P456"],
                "comparison_type": "vs_cohort"
            }
        )
        
        params = self.orchestrator._extract_comparison_parameters(comparison_intent)
        self.assertEqual(params["property_id"], "P456")
        self.assertEqual(params["comparison_type"], "vs_cohort")
    
    def test_tool_execution_error_handling(self):
        """Test error handling during tool execution."""
        # Create a failing tool
        failing_tools = {
            "get_property_metrics": MagicMock(side_effect=ValueError("Invalid property ID"))
        }
        
        # Create intent for testing
        intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["INVALID"]}
        )
        
        # Execute tools
        results = self.orchestrator.execute_tools(intent, failing_tools)
        
        # Verify error was captured
        self.assertEqual(len(results), 1)
        self.assertFalse(results[0].success)
        self.assertIsNotNone(results[0].error_message)
        self.assertIn("Invalid property ID", results[0].error_message)


class TestResultSynthesizer(unittest.TestCase):
    """Test cases for the ResultSynthesizer."""

    def setUp(self):
        """Set up test environment."""
        self.mock_llm_engine = MagicMock(spec=LLMEngine)
        self.synthesizer = ResultSynthesizer(self.mock_llm_engine)
    
    def test_synthesize_success_response(self):
        """Test synthesizing a response with successful tool results."""
        # Mock LLM response
        self.mock_llm_engine.create_completion.return_value = {
            "content": "Property P123 is performing well with 85% occupancy, which is 7% above the cohort average."
        }
        
        # Create successful tool results
        tool_results = [
            ToolResult(
                tool_name="get_property_metrics",
                success=True,
                data={
                    "occupancy_rate": 0.85,
                    "average_daily_rate": 150.25,
                    "revenue": 12500
                }
            ),
            ToolResult(
                tool_name="get_property_comparisons",
                success=True,
                data={
                    "property_id": "P123",
                    "cohort_avg_occupancy": 0.78,
                    "difference": 0.07
                }
            )
        ]
        
        # Synthesize response
        query = "How is property P123 performing compared to other properties?"
        parsed_intent = {"intent": "property_comparison"}
        
        response = self.synthesizer.synthesize_response(
            query,
            parsed_intent,
            tool_results
        )
        
        # Verify LLM was called
        self.mock_llm_engine.create_completion.assert_called_once()
        
        # Verify response
        self.assertEqual(
            response,
            "Property P123 is performing well with 85% occupancy, which is 7% above the cohort average."
        )
    
    def test_generate_error_response(self):
        """Test generating an error response when tools fail."""
        # Mock LLM response for error
        self.mock_llm_engine.create_completion.return_value = {
            "content": "I'm sorry, but I couldn't find information for property P999. Please verify the property ID and try again."
        }
        
        # Create failed tool results
        tool_results = [
            ToolResult(
                tool_name="get_property_metrics",
                success=False,
                data=None,
                error_message="Property P999 not found"
            )
        ]
        
        # Generate error response
        query = "How is property P999 performing?"
        response = self.synthesizer._generate_error_response(query, tool_results)
        
        # Verify LLM was called
        self.mock_llm_engine.create_completion.assert_called_once()
        
        # Verify error response - make assertion case insensitive
        self.assertIn("i'm sorry", response.lower())
    
    def test_generate_fallback_response(self):
        """Test generating fallback response when no tools were executed."""
        # Create parsed intent with clarification needed
        parsed_intent = {
            "intent": "clarification_needed",
            "clarification_needed": "Which property would you like me to analyze?"
        }
        
        # Generate fallback response
        query = "How is it performing?"
        response = self.synthesizer._generate_fallback_response(query, parsed_intent)
        
        # Verify clarification request was returned
        self.assertEqual(response, "Which property would you like me to analyze?")
        
        # Test standard fallback for property performance intent
        standard_intent = {"intent": "property_performance"}
        response = self.synthesizer._generate_fallback_response(query, standard_intent)
        
        # Verify standard fallback was generated
        self.assertIn("property performance metrics", response.lower())
        self.assertIn("more specific information", response.lower())


if __name__ == "__main__":
    unittest.main()
