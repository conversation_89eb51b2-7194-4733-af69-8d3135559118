"""
Tests for the LLM Engine component.
"""
import os
import unittest
import pytest
import json
from unittest.mock import patch
from dotenv import load_dotenv
from openai import OpenAI

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig
# Import get_vcr_instance from conftest
from conftest import get_vcr_instance

# Get the standardized VCR instance for this test file using the shared configuration
my_vcr = get_vcr_instance('llm_engine')

class TestLLMEngine(unittest.TestCase):
    """Test cases for LLMEngine."""

    def setUp(self):
        """Set up test environment."""
        # Load environment variables
        load_dotenv()
        
        # Get the real API key directly from environment
        self.api_key = os.environ.get('OPENAI_API_KEY')
        
        if not self.api_key or len(self.api_key) < 10:
            raise ValueError(f"Invalid or missing API key for tests: {self.api_key[:4]}... (length: {len(self.api_key) if self.api_key else 0})")
            
        print(f"Test setup using valid API key: {self.api_key[:4]}...")
        
        # Create our own direct OpenAI client with the real key
        self.direct_client = OpenAI(api_key=self.api_key)
        
        # Setup the LLM engine with explicit API key injection
        config = LLMConfig(model="gpt-4o-mini")
        
        # Create engine instance but replace its client with our direct client
        self.engine = LLMEngine(config=config)
        self.engine.client = self.direct_client
    
    def test_initialization(self):
        """Test LLMEngine initialization."""
        # Test model assignment via config
        self.assertEqual(self.engine.config.model, "gpt-4o-mini")
        
        # Test client creation
        self.assertIsNotNone(self.engine.client)
    
    @my_vcr.use_cassette('test_create_completion.yaml')
    def test_create_completion(self):
        """Test completion creation."""
        # Create completion
        messages = [{"role": "user", "content": "Hello, please give a short response."}]
        response = self.engine.create_completion(messages=messages)
        
        # Verify the response
        self.assertIsInstance(response, dict)
        self.assertIn("content", response)
        self.assertTrue(response["content"])  # Non-empty response
    
    @my_vcr.use_cassette('test_function_calling.yaml')
    def test_function_calling(self):
        """Test function calling capability."""
        # Define a test function
        tools = [
            {
                "type": "function",
                "function": {
                    "name": "get_property_details",
                    "description": "Get details about a property",
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "property_id": {
                                "type": "string",
                                "description": "The ID of the property"
                            },
                            "include_amenities": {
                                "type": "boolean",
                                "description": "Whether to include amenities"
                            }
                        },
                        "required": ["property_id"]
                    }
                }
            }
        ]
        
        # Create a test message
        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "I want to know about property ABC123 and its amenities"}
        ]
        
        # Call the function
        response = self.engine.create_completion(messages, tools=tools)
        
        # Check function call - handle both new and old API formats
        if 'tool_calls' in response:
            # New API format
            self.assertGreater(len(response['tool_calls']), 0)
            tool_call = response['tool_calls'][0]
            self.assertEqual(tool_call['function']['name'], 'get_property_details')
            
            # Parse function arguments - handle both string and dict formats
            if isinstance(tool_call['function']['arguments'], str):
                function_args = json.loads(tool_call['function']['arguments'])
            else:
                function_args = tool_call['function']['arguments']
                
            self.assertEqual(function_args['property_id'], "ABC123")
            self.assertTrue(function_args.get('include_amenities', False))
        elif 'function_call' in response:
            # Legacy API format
            self.assertEqual(response['function_call']['name'], 'get_property_details')
            
            # Parse function arguments - handle both string and dict formats
            if isinstance(response['function_call']['arguments'], str):
                function_args = json.loads(response['function_call']['arguments'])
            else:
                function_args = response['function_call']['arguments']
                
            self.assertEqual(function_args['property_id'], "ABC123")
            self.assertTrue(function_args.get('include_amenities', False))
        else:
            self.fail("Response contains neither tool_calls nor function_call: " + str(response))

if __name__ == "__main__":
    unittest.main()
