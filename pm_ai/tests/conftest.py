"""Test configuration and fixtures for PM_AI tests."""
import pytest
import vcr
import os
import sys
import json
from unittest.mock import patch, MagicMock
from pathlib import Path
from dotenv import load_dotenv
from openai import OpenAI

# Add project root to path
project_root = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(project_root))

# Load environment variables for tests
load_dotenv()

# Verify we have an API key
api_key = os.environ.get("OPENAI_API_KEY")
if not api_key:
    raise ValueError("No OPENAI_API_KEY found in environment. Tests require a valid API key.")

print(f"Using OpenAI API key starting with {api_key[:4]}... for tests")

# Create test fixtures directory
fixtures_path = Path(__file__).parent / 'fixtures'
fixtures_path.mkdir(parents=True, exist_ok=True)

# Configure VCR.py - standard configuration for all tests
vcr_path = fixtures_path / 'vcr_cassettes'
vcr_path.mkdir(parents=True, exist_ok=True)

# Standard VCR configuration to be used across all test files
# We're using a standard configuration for all tests to prevent inconsistencies
def get_vcr_instance(cassette_subdir=None):
    """Get a standardized VCR instance with consistent settings.
    
    Args:
        cassette_subdir: Optional subdirectory under the base vcr_path
            for organizing cassettes by test module
            
    Returns:
        Configured VCR instance with standardized settings
    """
    # Set up the cassette directory path
    if cassette_subdir:
        cassette_dir = vcr_path / cassette_subdir
        os.makedirs(str(cassette_dir), exist_ok=True)
    else:
        cassette_dir = vcr_path
        
    # Create and return the VCR instance with standardized settings
    return vcr.VCR(
        cassette_library_dir=str(cassette_dir),
        record_mode='new_episodes',  # Create new cassettes when needed
        filter_headers=['authorization'],  # Don't store API keys
        match_on=['uri', 'method'],  # More forgiving matching during development
        filter_query_parameters=['api-key', 'apikey', 'key'],
        decode_compressed_response=True
    )

# Create a default VCR instance for the fixture
my_vcr = get_vcr_instance()

# Expose VCR decorator for tests to use
@pytest.fixture
def vcr_config():
    """Return the configured VCR instance for tests to use."""
    return my_vcr

@pytest.fixture
def openai_client():
    """Create an OpenAI client with the real API key for testing."""
    return OpenAI(api_key=api_key)

@pytest.fixture
def mock_tiktoken():
    """Mock tiktoken to prevent actual token counting during tests."""
    mock_encoding = MagicMock()
    mock_encoding.encode.return_value = [1, 2, 3, 4, 5]  # Mock token IDs
    
    with patch("tiktoken.encoding_for_model") as mock_encoding_for_model:
        mock_encoding_for_model.return_value = mock_encoding
        yield mock_encoding
