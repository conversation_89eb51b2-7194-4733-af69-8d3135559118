#!/usr/bin/env python
"""
Test script for validating the Property Manager AI functionality.
This script tests the property management tools and agent responses.
"""

import os
import sys
import time
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent.parent.absolute()
sys.path.insert(0, str(project_root))

from dotenv import load_dotenv
from agents import Agent, Runner

# Import the property management tools
from pm_ai.tools.property_metrics import get_property_metrics
from pm_ai.tools.property_comparisons import get_property_comparisons, get_cohort_properties
from pm_ai.tools.property_recommendations import get_property_recommendations, get_outlier_statistics
from pm_ai.agents.property_manager import agent as property_manager_agent

# Load environment variables
load_dotenv()

def test_property_metrics():
    """Test the property metrics tool."""
    try:
        # Create an agent with just the property metrics tool for testing
        test_agent = Agent(
            name="PropertyMetricsTest",
            instructions="Return property performance metrics when asked.",
            tools=[get_property_metrics]
        )
        
        # Test with a specific property ID
        result = Runner.run_sync(test_agent, "How is property P123 performing this month?")
        output = result["final_output"]
        
        if "occupancy rate" in output.lower() and "average daily rate" in output.lower():
            print(f"✓ Property metrics test passed. Response: {output}")
        else:
            print(f"✗ Property metrics test failed. Response: {output}")
            return False
            
        return True
            
    except Exception as e:
        print(f"✗ Property metrics test failed with error: {e}")
        return False

def test_property_comparisons():
    """Test the property comparisons tool."""
    try:
        # Create an agent with just the property comparisons tool for testing
        test_agent = Agent(
            name="PropertyComparisonsTest",
            instructions="Compare properties when asked.",
            tools=[get_property_comparisons]
        )
        
        print("Testing property comparison query...")
        result = Runner.run_sync(test_agent, "Compare property P456 with similar properties.")
        output = result["final_output"]
        
        if "comparison" in output.lower() and "cohort" in output.lower():
            print(f"✓ Property comparison test passed. Response: {output}")
        else:
            print(f"✗ Property comparison test failed. Response: {output}")
            return False
            
        return True
        
    except Exception as e:
        print(f"✗ Property comparison test failed with error: {e}")
        return False

def test_agent_responses():
    """Test the full Property Manager AI agent with all tools."""
    try:
        # Use the imported property manager agent
        assistant = property_manager_agent
        
        test_queries = [
            "How is property P789 performing this month?",
            "Compare property P456 with similar properties.",
            "What recommendations do you have for property P123?",
            "Show me properties in the Downtown cohort."
        ]
        
        for query in test_queries:
            print(f"\nQuery: {query}")
            start_time = time.time()
            result = Runner.run_sync(assistant, query)
            elapsed = time.time() - start_time
            print(f"Response ({elapsed:.2f}s):")
            print(result.final_output)
        
        print("\n✓ All agent response tests passed!")
        return True
    
    except Exception as e:
        print(f"\n✗ Agent response test failed: {e}")
        return False

def main():
    """Run all tests and return True if all pass."""
    print("\n============= Property Manager AI Tests =============")
    
    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("\n⚠️ OPENAI_API_KEY not set in environment - this may be required in the future")
        print("Please set up your .env file with valid API key")
        # Continue with tests since we're using mock data
        
    # Test property metrics tool
    print("\n📊 Testing property metrics tool...")
    if not test_property_metrics():
        print("\n❌ Property metrics tool test failed. Stopping.")
        return False
    
    # Test property comparisons tool
    print("\n🔄 Testing property comparisons tool...")
    if not test_property_comparisons():
        print("\n❌ Property comparisons tool test failed. Stopping.")
        return False
    
    # Test agent responses
    print("\n=== Testing Agent Responses ===")
    if not test_agent_responses():
        print("\n❌ Agent responses test failed. Stopping.")
        return False
    
    print("\n=== Test Summary ===")
    print("✓ All tests passed!")
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
