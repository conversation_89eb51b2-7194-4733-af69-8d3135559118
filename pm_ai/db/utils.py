"""
Database utility functions for PM_AI.
"""
import sqlite3
from datetime import datetime, timedelta
import calendar
import re
from pm_ai.config.settings import DB_PATH

def get_db_connection():
    """Create and return a database connection."""
    conn = sqlite3.connect(DB_PATH)
    conn.row_factory = sqlite3.Row  # Return rows as dictionaries
    return conn

def parse_period(period_str):
    """
    Parse a time period string into start and end dates.
    
    Examples:
    - "April" -> April 1st to April 30th of current year
    - "April 2024" -> April 1st to April 30th, 2024
    - "Q2" -> April 1st to June 30th of current year
    - "2024" -> January 1st to December 31st, 2024
    """
    current_year = datetime.now().year
    period_str = period_str.strip().lower()
    
    # Check for month name
    months = {
        "january": 1, "february": 2, "march": 3, "april": 4,
        "may": 5, "june": 6, "july": 7, "august": 8,
        "september": 9, "october": 10, "november": 11, "december": 12
    }
    
    for month_name, month_num in months.items():
        if month_name in period_str:
            # Check if year is specified
            year_match = re.search(r'\b(20\d{2})\b', period_str)
            year = int(year_match.group(1)) if year_match else current_year
            
            # Get first and last day of month
            first_day = datetime(year, month_num, 1)
            last_day = datetime(year, month_num, calendar.monthrange(year, month_num)[1])
            
            return first_day.strftime('%Y-%m-%d'), last_day.strftime('%Y-%m-%d')
    
    # Check for quarters (Q1, Q2, Q3, Q4)
    quarter_match = re.search(r'q([1-4])', period_str)
    if quarter_match:
        quarter = int(quarter_match.group(1))
        year_match = re.search(r'\b(20\d{2})\b', period_str)
        year = int(year_match.group(1)) if year_match else current_year
        
        start_month = (quarter - 1) * 3 + 1
        end_month = quarter * 3
        
        start_date = datetime(year, start_month, 1)
        end_date = datetime(year, end_month, calendar.monthrange(year, end_month)[1])
        
        return start_date.strftime('%Y-%m-%d'), end_date.strftime('%Y-%m-%d')
    
    # Check for year
    year_match = re.search(r'\b(20\d{2})\b', period_str)
    if year_match:
        year = int(year_match.group(1))
        return f"{year}-01-01", f"{year}-12-31"
    
    # If nothing matches, assume it's referring to the current month
    today = datetime.now()
    first_day = datetime(today.year, today.month, 1)
    last_day = datetime(today.year, today.month, calendar.monthrange(today.year, today.month)[1])
    
    return first_day.strftime('%Y-%m-%d'), last_day.strftime('%Y-%m-%d')

def get_spending_data(period, category=None):
    """
    Get spending data for the specified period and optional category.
    
    Args:
        period: String representing time period (e.g., "April", "Q2 2025")
        category: Optional category to filter by
        
    Returns:
        Dictionary with total spending and breakdown by category
    """
    try:
        start_date, end_date = parse_period(period)
    except Exception as e:
        return {
            "error": f"Failed to parse time period: {str(e)}",
            "valid_formats": "Try formats like 'April', 'April 2025', 'Q2', or '2025'"
        }
    
    conn = get_db_connection()
    cursor = conn.cursor()
    
    # Build query based on whether category is specified
    query = """
    SELECT category, SUM(amount) as total
    FROM transactions
    WHERE date BETWEEN ? AND ?
    """
    params = [start_date, end_date]
    
    if category:
        query += " AND category LIKE ?"
        params.append(f"%{category}%")
    
    query += " GROUP BY category ORDER BY total DESC"
    
    try:
        cursor.execute(query, params)
        categories = cursor.fetchall()
        
        # Get overall total
        total_query = "SELECT SUM(amount) as grand_total FROM transactions WHERE date BETWEEN ? AND ?"
        total_params = [start_date, end_date]
        
        if category:
            total_query += " AND category LIKE ?"
            total_params.append(f"%{category}%")
            
        cursor.execute(total_query, total_params)
        grand_total = cursor.fetchone()['grand_total'] or 0
        
        # Format the result
        result = {
            "period": {
                "description": period,
                "start_date": start_date,
                "end_date": end_date
            },
            "total_spending": round(grand_total, 2),
            "categories": []
        }
        
        for cat in categories:
            result["categories"].append({
                "category": cat["category"],
                "amount": round(cat["total"], 2),
                "percentage": round((cat["total"] / grand_total) * 100, 1) if grand_total > 0 else 0
            })
        
        return result
    
    except Exception as e:
        return {"error": f"Database query failed: {str(e)}"}
    finally:
        conn.close()
