"""
Database setup script for PM_<PERSON>.
Creates a SQLite database with transaction schema and populates with sample data.
"""
import sqlite3
import random
from datetime import datetime, timedelta
from pm_ai.config.settings import DB_PATH

# Categories for transactions
CATEGORIES = ["Groceries", "Dining", "Entertainment", "Transportation", "Utilities", "Shopping"]

def setup_database():
    """Set up the SQLite database with schema and sample data."""
    # Create database connection
    conn = sqlite3.connect(DB_PATH)
    cursor = conn.cursor()
    
    # Create transactions table
    cursor.execute('''
    CREATE TABLE IF NOT EXISTS transactions (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        date TEXT NOT NULL,
        amount REAL NOT NULL,
        category TEXT NOT NULL,
        description TEXT
    )
    ''')
    
    # Check if we already have data
    cursor.execute("SELECT COUNT(*) FROM transactions")
    if cursor.fetchone()[0] > 0:
        print(f"Database at {DB_PATH} already contains data. Skipping sample data creation.")
        conn.close()
        return
    
    # Generate sample transactions for the past year
    today = datetime.now()
    one_year_ago = today - timedelta(days=365)
    
    sample_data = []
    current_date = one_year_ago
    
    # Generate transactions with higher frequency in April for testing
    while current_date <= today:
        # Determine number of transactions for this day
        # More transactions in April for testing
        is_april = current_date.month == 4
        num_transactions = random.randint(0, 3)
        if is_april:
            num_transactions += 2  # More transactions in April
        
        for _ in range(num_transactions):
            # Generate random transaction
            category = random.choice(CATEGORIES)
            
            # Vary amounts by category
            if category == "Groceries":
                amount = round(random.uniform(10, 150), 2)
                description = random.choice(["Supermarket", "Farmer's Market", "Local Store"])
            elif category == "Dining":
                amount = round(random.uniform(15, 100), 2)
                description = random.choice(["Restaurant", "Cafe", "Fast Food"])
            elif category == "Entertainment":
                amount = round(random.uniform(10, 50), 2)
                description = random.choice(["Movies", "Concert", "Subscription"])
            elif category == "Transportation":
                amount = round(random.uniform(5, 75), 2)
                description = random.choice(["Gas", "Public Transit", "Ride Share"])
            elif category == "Utilities":
                amount = round(random.uniform(50, 200), 2)
                description = random.choice(["Electricity", "Water", "Internet"])
            else:  # Shopping
                amount = round(random.uniform(20, 200), 2)
                description = random.choice(["Clothing", "Electronics", "Home Goods"])
            
            # Format date as YYYY-MM-DD
            date_str = current_date.strftime('%Y-%m-%d')
            
            sample_data.append((date_str, amount, category, description))
        
        # Move to next day
        current_date += timedelta(days=1)
    
    # Insert sample data
    cursor.executemany(
        "INSERT INTO transactions (date, amount, category, description) VALUES (?, ?, ?, ?)",
        sample_data
    )
    
    # Commit changes and close connection
    conn.commit()
    conn.close()
    
    print(f"Database setup complete. Created {len(sample_data)} sample transactions at {DB_PATH}.")

if __name__ == "__main__":
    setup_database()
