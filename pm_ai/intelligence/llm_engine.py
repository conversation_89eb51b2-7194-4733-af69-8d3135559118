"""
Core LLM engine for OpenAI GPT-4 integration.
Handles API calls, error handling, rate limiting, and response processing.
"""
import os
import time
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from openai import OpenAI
import tiktoken

@dataclass
class LLMConfig:
    """Configuration for LLM engine."""
    model: str = "gpt-4o-mini"  # Default to a more efficient model for development
    max_tokens: int = 4096
    temperature: float = 0.1
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: int = 30

class LLMEngine:
    """
    Core engine for LLM interactions with OpenAI GPT-4.
    
    Responsibilities:
    - Manage OpenAI API client and configuration
    - Handle function calling for tool selection
    - Implement retry logic and error handling
    - Track token usage and costs
    - Provide conversation context management
    """
    
    def __init__(self, config: Optional[LLMConfig] = None, api_key: Optional[str] = None):
        """Initialize the LLM Engine with configuration.
        
        Args:
            config: LLMConfig object with model settings
            api_key: Optional explicit API key. If None, will load from environment
        """
        self.config = config or LLMConfig()
        
        # Try multiple sources for API key with explicit parameter having highest priority
        self.api_key = api_key or os.environ.get("OPENAI_API_KEY") or os.getenv("OPENAI_API_KEY")
        
        # Ensure we never use the string 'mock-api-key'
        if self.api_key == "mock-api-key":
            raise ValueError("Cannot use 'mock-api-key' for OpenAI API calls. Please provide a real API key.")
        
        # Debug logging
        if self.api_key:
            safe_prefix = self.api_key[:4] + "..." if len(self.api_key) > 4 else "[empty]"
            self.logger = logging.getLogger(__name__)
            self.logger.info(f"Initializing LLM engine with API key: {safe_prefix}")
        else:
            self.logger = logging.getLogger(__name__)
            self.logger.error("No API key provided for LLM engine")
            raise ValueError("OpenAI API key is required and was not found")
            
        # Initialize client with our validated API key
        self.client = OpenAI(api_key=self.api_key)
        self.encoding = tiktoken.encoding_for_model(self.config.model)
    
    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        return len(self.encoding.encode(text))
    
    def create_completion(
        self,
        messages: List[Dict[str, str]],
        functions: Optional[List[Dict[str, Any]]] = None,
        function_call: Optional[Dict[str, str]] = None,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: Optional[str or Dict[str, Any]] = None,
        temperature: Optional[float] = None,
        max_tokens: Optional[int] = None,
        response_format: Optional[Dict[str, str]] = None,
    ) -> Dict:
        """Create a completion using the OpenAI API.

        Args:
            messages: List of messages to send to the API
            functions: [DEPRECATED] List of functions to make available to the model
            function_call: [DEPRECATED] Optional function to call
            tools: List of tools to make available to the model (replaces functions)
            tool_choice: Optional tool choice specification (replaces function_call)
            temperature: Optional temperature parameter
            max_tokens: Optional maximum tokens parameter
            response_format: Optional format for the response

        Returns:
            The API response as a dictionary
        """
        # Handle optional parameters
        model = self.config.model
        temp = temperature if temperature is not None else self.config.temperature

        try:
            # Handle max_tokens and response_format conditionally
            request_kwargs = {}
            if max_tokens is not None:
                request_kwargs["max_tokens"] = max_tokens
            if response_format is not None:
                request_kwargs["response_format"] = response_format
            
            # Handle tools vs. functions (new API vs old API)
            if tools is not None:
                # Use the new tools API
                request_kwargs["tools"] = tools
                if tool_choice is not None:
                    request_kwargs["tool_choice"] = tool_choice
            elif functions is not None:
                # Use the deprecated functions API for backward compatibility
                request_kwargs["functions"] = functions
                if function_call is not None:
                    request_kwargs["function_call"] = function_call
            
            self.logger.debug(f"Creating completion with model {model} and {len(messages)} messages")
            
            # Create the completion
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temp,
                **request_kwargs
            )
            
            # Return the response message as a dictionary
            response_dict = response.choices[0].message.model_dump()
            self.logger.debug(f"Completion returned with keys: {list(response_dict.keys())}")
            return response_dict
        except Exception as e:
            self.logger.error(f"Error creating completion: {e}")
            raise
    
    def _process_response(self, response, messages: List[Dict]) -> Dict[str, Any]:
        """Process OpenAI API response and extract relevant information."""
        choice = response.choices[0]
        message = choice.message
        
        result = {
            "content": message.content,
            "function_call": None,
            "finish_reason": choice.finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }
        
        # Handle function calls
        if hasattr(message, 'function_call') and message.function_call:
            result["function_call"] = {
                "name": message.function_call.name,
                "arguments": json.loads(message.function_call.arguments)
            }
        
        return result
