"""
Natural language intent recognition for property management queries.
Replaces hardcoded pattern matching with LLM-powered understanding.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

from .llm_engine import LLMEngine

class QueryIntent(Enum):
    """Possible user intents for property management queries."""
    PROPERTY_PERFORMANCE = "property_performance"
    PROPERTY_COMPARISON = "property_comparison" 
    RECOMMENDATIONS = "recommendations"
    OUTLIER_ANALYSIS = "outlier_analysis"
    COHORT_ANALYSIS = "cohort_analysis"
    GENERAL_INQUIRY = "general_inquiry"
    CLARIFICATION_NEEDED = "clarification_needed"

@dataclass
class ParsedIntent:
    """Structured representation of parsed user intent."""
    intent: QueryIntent
    confidence: float
    parameters: Dict[str, Any]
    clarification_needed: Optional[str] = None
    suggested_actions: List[str] = None

class IntentParser:
    """
    Natural language intent parser for property management queries.
    
    Uses GPT-4 to understand user intent and extract parameters without
    relying on hardcoded patterns or regex matching.
    """
    
    def __init__(self, llm_engine: LLMEngine):
        self.llm_engine = llm_engine
        self.system_prompt = self._create_system_prompt()
    
    def parse_query(self, query: str, context: Optional[Dict] = None) -> ParsedIntent:
        """
        Parse user query to extract intent and parameters.
        
        Args:
            query: User's natural language query
            context: Optional conversation context
            
        Returns:
            ParsedIntent with structured information
        """
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": self._format_query(query, context)}
        ]
        
        functions = [self._get_intent_extraction_function()]
        
        response = self.llm_engine.create_completion(
            messages=messages,
            functions=functions,
            function_call={"name": "extract_intent"}
        )
        
        if response["function_call"]:
            return self._process_intent_response(response["function_call"]["arguments"])
        else:
            # Fallback if function calling fails
            return ParsedIntent(
                intent=QueryIntent.GENERAL_INQUIRY,
                confidence=0.5,
                parameters={},
                clarification_needed="I couldn't understand your request. Could you please rephrase?"
            )
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for intent recognition."""
        return """You are an expert at understanding property management queries.

Your task is to analyze user queries about vacation rental properties and extract:
1. The user's intent (what they want to accomplish)
2. Relevant parameters (property IDs, time periods, metrics, etc.)
3. Confidence level in your interpretation
4. Whether clarification is needed

Property Context:
- Properties are identified by IDs like P123, P456, P789
- Cohorts include: beachfront, downtown, mountain_view, luxury
- Time periods can be: last_30_days, last_quarter, last_year, or custom ranges
- Common metrics: occupancy_rate, average_daily_rate, total_revenue

Handle natural language variations like:
- "How's my beachfront place doing?" → property_performance for beachfront cohort
- "Compare P123 to similar properties" → property_comparison for P123
- "Any recommendations for the downtown rental?" → recommendations for downtown cohort
- "What's unusual about P456?" → outlier_analysis for P456

Always extract structured parameters and indicate confidence level."""
    
    def _format_query(self, query: str, context: Optional[Dict]) -> str:
        """Format query with context for LLM processing."""
        formatted = f"User Query: {query}"
        
        if context:
            formatted += f"\n\nContext: {json.dumps(context, indent=2)}"
        
        return formatted
    
    def _get_intent_extraction_function(self) -> Dict:
        """Define function schema for intent extraction."""
        return {
            "name": "extract_intent",
            "description": "Extract intent and parameters from property management query",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {
                        "type": "string",
                        "enum": [intent.value for intent in QueryIntent],
                        "description": "Primary intent of the user query"
                    },
                    "confidence": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "description": "Confidence level in intent recognition (0.0-1.0)"
                    },
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "property_ids": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific property IDs mentioned (e.g., P123, P456)"
                            },
                            "cohort": {
                                "type": "string",
                                "enum": ["beachfront", "downtown", "mountain_view", "luxury"],
                                "description": "Property cohort/group mentioned"
                            },
                            "time_period": {
                                "type": "string",
                                "description": "Time period for analysis (e.g., last_30_days, this_month)"
                            },
                            "metrics": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific metrics mentioned (occupancy, revenue, etc.)"
                            },
                            "comparison_type": {
                                "type": "string",
                                "description": "Type of comparison requested (vs cohort, vs previous period)"
                            }
                        }
                    },
                    "clarification_needed": {
                        "type": "string",
                        "description": "Question to ask user if query is ambiguous"
                    },
                    "suggested_actions": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Suggested follow-up actions or tools to use"
                    }
                },
                "required": ["intent", "confidence", "parameters"]
            }
        }
    
    def _process_intent_response(self, arguments: Dict) -> ParsedIntent:
        """Process function call response into ParsedIntent object."""
        # Handle string arguments from function call
        if isinstance(arguments, str):
            try:
                arguments = json.loads(arguments)
            except json.JSONDecodeError:
                return ParsedIntent(
                    intent=QueryIntent.CLARIFICATION_NEEDED,
                    confidence=0.0,
                    parameters={},
                    clarification_needed="There was an error processing your request."
                )
                
        return ParsedIntent(
            intent=QueryIntent(arguments["intent"]),
            confidence=arguments["confidence"],
            parameters=arguments["parameters"],
            clarification_needed=arguments.get("clarification_needed"),
            suggested_actions=arguments.get("suggested_actions", [])
        )
