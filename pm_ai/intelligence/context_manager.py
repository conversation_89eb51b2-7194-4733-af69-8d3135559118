"""
Conversation context management for enhanced natural language understanding.
"""
from typing import Dict, List, Optional, Any
import re

from ..conversation.session_manager import SessionManager

class ContextManager:
    """
    Manages conversation context for more natural interactions.
    
    Responsibilities:
    - Process reference resolution (e.g., 'it', 'that property')
    - Track entities across conversation turns
    - Enrich queries with context for better understanding
    - Help LLM maintain coherence across conversation turns
    """
    
    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
    
    def enrich_query(self, query: str, session_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Enrich query with conversation context.
        
        Args:
            query: The raw user query
            session_id: Optional session ID (uses active session if None)
            
        Returns:
            Dictionary with enriched query and context
        """
        # Get or create session
        if session_id:
            self.session_manager.get_or_create_session(session_id)
        
        # Get conversation context
        context = self.session_manager.build_conversation_context()
        
        # Perform reference resolution on the query
        enriched_query = self._resolve_references(query, context)
        
        return {
            "original_query": query,
            "enriched_query": enriched_query,
            "context": context
        }
    
    def update_context_after_response(
        self,
        parsed_intent: Dict[str, Any],
        response_content: str,
        tools_used: List[str]
    ):
        """
        Update context after generating a response.
        
        Args:
            parsed_intent: The parsed intent from the query
            response_content: The response content generated
            tools_used: Tools used to generate the response
        """
        # Extract entities from the response
        entities = self._extract_entities_from_response(response_content)
        
        # Update session context with new entities
        self.session_manager.update_context({
            "last_entities": entities,
            "last_intent": parsed_intent,
            "last_tools": tools_used
        })
    
    def _resolve_references(self, query: str, context: Dict[str, Any]) -> str:
        """
        Resolve references like 'it', 'that property', 'the beachfront one'.
        
        Args:
            query: The raw user query
            context: Conversation context
            
        Returns:
            Query with resolved references when possible
        """
        # Skip if no history
        if not context or not context.get('history'):
            return query
            
        enriched = query
        
        # Simple pronoun resolution
        references = context.get('references', {})
        properties = references.get('properties', {})
        last_property = None
        
        # Get the most recently mentioned property
        if properties:
            last_property = max(properties.items(), key=lambda x: x[1])[0]
        
        # Replace pronouns if we have a referenced property
        if last_property:
            # Define patterns for pronoun replacement
            pronouns = [
                r'\bit\b', 
                r'that property', 
                r'this property', 
                r'the property',
                r'that rental',
                r'this rental',
                r'the rental'
            ]
            
            for pattern in pronouns:
                enriched = re.sub(
                    pattern, 
                    f"property {last_property}", 
                    enriched, 
                    flags=re.IGNORECASE
                )
        
        # Handle time period references
        time_periods = references.get('time_periods', {})
        last_time_period = None
        
        # Get the most recently mentioned time period
        if time_periods:
            last_time_period = max(time_periods.items(), key=lambda x: x[1])[0]
        
        # Replace time period references
        if last_time_period:
            time_patterns = [
                r'that period', 
                r'that time', 
                r'this period', 
                r'that time period',
                r'the same period'
            ]
            
            for pattern in time_patterns:
                enriched = re.sub(
                    pattern, 
                    f"{last_time_period}", 
                    enriched, 
                    flags=re.IGNORECASE
                )
        
        return enriched
    
    def _extract_entities_from_response(self, response: str) -> Dict[str, Any]:
        """
        Extract entities mentioned in the response for future reference.
        
        Args:
            response: The system response text
            
        Returns:
            Dictionary of extracted entities
        """
        entities = {
            "properties": [],
            "cohorts": [],
            "metrics": []
        }
        
        # Extract property IDs (P followed by numbers)
        property_pattern = r'P\d{3}'
        properties = re.findall(property_pattern, response)
        if properties:
            entities["properties"] = list(set(properties))
        
        # Extract cohorts
        cohort_patterns = [
            r'beachfront',
            r'downtown',
            r'mountain_view',
            r'mountain view',
            r'luxury'
        ]
        
        for pattern in cohort_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                # Normalize to the standard format
                normalized = pattern.replace(' ', '_').lower()
                entities["cohorts"].append(normalized)
        
        # Extract metrics
        metric_patterns = [
            r'occupancy rate',
            r'occupancy',
            r'average daily rate',
            r'ADR',
            r'revenue',
            r'total revenue'
        ]
        
        for pattern in metric_patterns:
            if re.search(pattern, response, re.IGNORECASE):
                # Normalize to snake_case
                normalized = pattern.lower().replace(' ', '_')
                if normalized == "adr":
                    normalized = "average_daily_rate"
                entities["metrics"].append(normalized)
        
        # Remove duplicates
        entities["cohorts"] = list(set(entities["cohorts"]))
        entities["metrics"] = list(set(entities["metrics"]))
        
        return entities
