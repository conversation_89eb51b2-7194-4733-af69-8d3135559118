# Property Manager AI

A Python-based AI assistant for property managers to analyze vacation rental performance data.

## Project Overview

Property Manager AI is powered by OpenAI's agents framework. It can:

1. Query and analyze property performance metrics
2. Compare properties against similar properties in their cohort
3. Provide recommendations for property optimization
4. Identify outlier statistics for properties

## Directory Structure

```
pm_ai/
├── agents/             # Agent implementation
│   ├── __init__.py     # Core agent definitions
│   └── property_manager.py # Property Manager agent
├── config/             # Configuration settings
├── data/               # JSON data files
│   ├── property_metrics.json       # Property performance data
│   ├── property_comparisons.json   # Property comparison data
│   ├── property_recommendations.json # Recommendations data
│   ├── property_outliers.json      # Outlier statistics
│   └── property_cohorts.json       # Cohort property data
├── docs/               # Documentation files
│   └── ERROR_LOG.md    # Error tracking and learnings
├── tests/              # Test suite
│   └── test_agent.py   # Property Manager AI tests
├── tools/              # Tool implementations
│   ├── property_metrics.py       # Property metrics tool
│   ├── property_comparisons.py   # Property comparisons tool
│   └── property_recommendations.py # Recommendations tool
└── main.py             # Main entry point
```

## Setup Instructions

### Prerequisites

- Python 3.8+
- pip (dependency management)

### Environment Variables

Create a `.env` file in the project root with:

```
OPENAI_API_KEY=your_openai_api_key
```

Note: While the OPENAI_API_KEY is included for future LLM integration, the current MVP uses static JSON data files and doesn't require API access.

### Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   pip install -r requirements.txt
   ```
## Usage

Run the interactive assistant:

```bash
python -m pm_ai.main
```

You can ask questions like:
- "How is property P123 performing this month?"
- "Compare property P456 with similar properties."
- "What recommendations do you have for property P123?"
- "Show me properties in the Downtown cohort."
- "What are the outlier statistics for property P789?"

## Testing

Run the test suite:

```bash
python -m pm_ai.tests.test_agent
```

## Data Files

The Property Manager AI uses JSON data files for the MVP implementation:

| File                     | Description                                      |
|--------------------------|--------------------------------------------------|
| property_metrics.json    | Performance metrics for individual properties    |
| property_comparisons.json| Comparison data between properties               |
| property_recommendations.json | Recommendations for property improvements   |
| property_outliers.json   | Statistical outliers for various property metrics|
| property_cohorts.json    | Groups of related properties by location/type    |

## Time Period Parsing

The application supports natural language time periods for property performance:

- Recent periods: "this month", "last quarter", "past year"
- Month names: "April", "June 2025"
- Quarters: "Q1", "Q4 2024"
- Years: "2023", "2025"

## Future Improvements

- Integrate real-time property data from PMS systems
- Implement LLM-based intent recognition for more complex queries
- Add dynamic dashboards and data visualizations
- Expand property recommendations with market analysis
- Enable performance forecasting and trend analysis
- Support multiple property portfolios and user accounts

## Development

To contribute to Property Manager AI development:

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Update the test suite with tests for your feature
5. Update the ERROR_LOG.md with any lessons learned
6. Submit a pull request
