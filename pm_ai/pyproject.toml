[tool.poetry]
name = "pm_ai"
version = "0.1.0"
description = "Personal Money AI Assistant"
authors = ["Your Name <<EMAIL>>"]
readme = "README.md"

[tool.poetry.dependencies]
python = "^3.8"
openai-agents = "^0.1.0"
requests = "^2.31.0"
python-dotenv = "^1.0.0"

[tool.poetry.dev-dependencies]
pytest = "^7.3.1"

[build-system]
requires = ["poetry-core>=1.0.0"]
build-backend = "poetry.core.masonry.api"

[tool.poetry.scripts]
pm_ai = "pm_ai.main:main"
