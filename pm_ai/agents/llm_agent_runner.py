"""
LLM-powered agent runner that orchestrates the entire conversational flow.
"""
from typing import Dict, List, Optional, Any, Callable
import logging
import os
from dotenv import load_dotenv

from ..intelligence.llm_engine import LLMEngine
from ..intelligence.intent_parser import Intent<PERSON><PERSON><PERSON>, ParsedIntent
from ..intelligence.context_manager import ContextManager
from ..conversation.session_manager import SessionManager
from ..conversation.memory_store import MemoryStore
from ..integration.tool_orchestrator import ToolOrchestrator
from ..integration.result_synthesizer import ResultSynthesizer

class LLMAgentRunner:
    """
    Orchestrates the entire LLM-powered agent conversation flow.
    
    Ties together all components:
    - LLM Engine for AI capabilities
    - Intent Parser for query understanding
    - Context Manager for conversation context
    - Tool Orchestrator for tool selection and execution
    - Result Synthesizer for response generation
    """
    
    def __init__(self, available_tools: Optional[Dict[str, Callable]] = None):
        # Load environment variables
        load_dotenv()
        
        # Set up logging
        self.logger = logging.getLogger(__name__)
        
        # Initialize OpenAI API key
        self.api_key = os.environ.get("OPENAI_API_KEY")
        if not self.api_key:
            raise ValueError(
                "OpenAI API key not found. Please set OPENAI_API_KEY environment variable."
            )
        
        # Get model configuration
        self.model = os.environ.get("OPENAI_MODEL", "gpt-4o-mini")
        
        # Initialize components
        self.available_tools = available_tools or {}
        self._initialize_components()
        
        # Session tracking
        self.active_session_id = None
    
    def _initialize_components(self):
        """Initialize all components of the agent."""
        # Core LLM engine
        from ..intelligence.llm_engine import LLMConfig
        config = LLMConfig(model=self.model)
        self.llm_engine = LLMEngine(config=config)
        
        # Memory and context components
        self.memory_store = MemoryStore()
        self.session_manager = SessionManager(self.memory_store)
        self.context_manager = ContextManager(self.session_manager)
        
        # NLP understanding components
        self.intent_parser = IntentParser(self.llm_engine)
        
        # Tool orchestration components
        self.tool_orchestrator = ToolOrchestrator(self.llm_engine)
        self.result_synthesizer = ResultSynthesizer(self.llm_engine)
    
    def process_query(
        self,
        query: str,
        user_id: Optional[str] = None,
        session_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Process a user query and generate a response.
        
        Args:
            query: The user's query
            user_id: Optional user identifier
            session_id: Optional session identifier
            
        Returns:
            Dictionary with response and metadata
        """
        try:
            # Get or create session
            self.active_session_id = self.session_manager.get_or_create_session(session_id)
            
            # Enrich query with conversation context
            enriched_data = self.context_manager.enrich_query(query, self.active_session_id)
            enriched_query = enriched_data["enriched_query"]
            conversation_context = enriched_data["context"]
            
            self.logger.info(f"Processing query: '{query}' (enriched: '{enriched_query}')")
            
            # Parse intent
            parsed_intent = self.intent_parser.parse_query(
                enriched_query,
                conversation_context
            )
            
            # Check if clarification is needed
            if parsed_intent.clarification_needed:
                self.logger.info("Clarification needed")
                return {
                    "response": parsed_intent.clarification_needed,
                    "session_id": self.active_session_id,
                    "parsed_intent": parsed_intent.__dict__,
                    "tools_used": []
                }
            
            # Select and execute tools
            tool_results = self.tool_orchestrator.execute_tools(
                parsed_intent,
                self.available_tools
            )
            
            tools_used = [result.tool_name for result in tool_results]
            self.logger.info(f"Tools used: {', '.join(tools_used) if tools_used else 'none'}")
            
            # Generate response
            response = self.result_synthesizer.synthesize_response(
                query,
                parsed_intent.__dict__,
                tool_results,
                conversation_context
            )
            
            # Update conversation context
            self.session_manager.add_interaction(
                query,
                parsed_intent.__dict__,
                response,
                tools_used
            )
            
            self.context_manager.update_context_after_response(
                parsed_intent.__dict__,
                response,
                tools_used
            )
            
            return {
                "response": response,
                "session_id": self.active_session_id,
                "parsed_intent": parsed_intent.__dict__,
                "tools_used": tools_used
            }
            
        except Exception as e:
            self.logger.error(f"Error processing query: {str(e)}", exc_info=True)
            return {
                "response": f"I encountered an error while processing your request: {str(e)}",
                "session_id": self.active_session_id,
                "error": str(e)
            }
    
    def register_tool(self, name: str, tool_fn: Callable):
        """
        Register a new tool with the agent.
        
        Args:
            name: Tool name
            tool_fn: Tool function
        """
        self.available_tools[name] = tool_fn
        self.logger.info(f"Registered tool: {name}")
    
    def get_session_id(self) -> Optional[str]:
        """Get current session ID."""
        return self.active_session_id
    
    def set_session_id(self, session_id: str):
        """Set active session ID."""
        self.active_session_id = session_id
        self.session_manager.get_or_create_session(session_id)
