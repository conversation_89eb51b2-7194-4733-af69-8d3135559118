"""
PM_AI agent for property management queries and analytics.
"""
import sys
import os

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from pm_ai.agents import Agent, Runner
from pm_ai.tools.property_metrics import get_property_metrics
from pm_ai.tools.property_comparisons import get_property_comparisons, get_cohort_properties
from pm_ai.tools.property_recommendations import get_property_recommendations, get_outlier_statistics

def create_agent():
    """
    Create the Property Manager AI agent.
    """
    return Agent(
        name="Property_Manager_AI",
        instructions="""
        Answer property management questions for vacation rental property owners. 
        
        For property performance questions:
        - Use the get_property_metrics tool to fetch performance data
        - Present key metrics like occupancy rate, average daily rate (ADR), and revenue
        - Format currency values appropriately (e.g., $1,234.56)
        
        For property comparison questions:
        - Use the get_property_comparisons tool for comparing against similar properties
        - Use the get_cohort_properties tool to find properties in the same group/area
        - Explain how the property's performance compares to similar properties
        
        For recommendations and insights:
        - Use the get_property_recommendations tool to suggest improvements
        - Use the get_outlier_statistics tool to highlight unusual performance
        - Provide clear, actionable recommendations with supporting data
        
        When answering questions:
        - Be concise but thorough
        - Focus on data-driven insights
        - Always provide context for any metrics or recommendations
        - Use professional language appropriate for property management
        
        Available properties: P123, P456, P789, P101, P202, P303, P404
        Available cohorts: beachfront, downtown, mountain_view, luxury
        """,
        tools=[
            get_property_metrics,
            get_property_comparisons,
            get_cohort_properties, 
            get_property_recommendations,
            get_outlier_statistics
        ],
    )

# Create the default agent instance
agent = create_agent()

if __name__ == "__main__":
    # Run a test query when script is executed directly
    result = Runner.run_sync(
        agent,
        "How is property P123 performing this month?"
    )
    print(result.final_output)
