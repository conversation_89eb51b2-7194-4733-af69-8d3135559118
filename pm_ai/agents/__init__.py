"""
Agent and Runner implementations for PM_AI.
"""
import os
import json
from typing import Dict, List, Callable, Optional, Any, TypedDict, Union

class CompletionResult(TypedDict):
    """Result from a tool completion."""
    final_output: str

class Agent:
    """
    Agent class for PM_AI. Wraps a large language model with specific instructions and tools.
    """
    def __init__(self, name: str, instructions: str, tools: List[Callable]):
        self.name = name
        self.instructions = instructions
        self.tools = tools
        
    def get_tool_by_name(self, tool_name: str) -> Optional[Callable]:
        """Get a tool function by its name."""
        for tool in self.tools:
            if tool.__name__ == tool_name:
                return tool
        return None

class Runner:
    """
    Runner for executing Agent interactions.
    """
    @staticmethod
    def run_sync(agent: Agent, query: str) -> CompletionResult:
        """
        Process a query through the agent and return the result.
        
        For <PERSON>, this is a simplistic implementation that doesn't actually call an LLM.
        Instead, it uses a set of hardcoded responses for specific query patterns.
        """
        # Very simple query matching for MVP demo purposes
        query = query.lower()
        
        # Default responses for each type of query
        if "how is property" in query and "performing" in query:
            # Extract property ID from the query
            import re
            property_match = re.search(r'property\s+([\w\d]+)', query)
            if property_match:
                property_id = property_match.group(1).upper()
                # Call the appropriate tool
                try:
                    tool = agent.get_tool_by_name("get_property_metrics")
                    if tool:
                        period = "last_30_days" if "month" in query else "last_quarter" if "quarter" in query else "last_year" if "year" in query else "last_30_days"
                        result = tool(property_id, period)
                        
                        if "error" in result:
                            return {"final_output": result["error"]}
                            
                        # Format the response
                        output = f"Performance metrics for property {property_id} ({period}):\n\n"
                        output += f"• Occupancy Rate: {result['occupancy_rate']}%\n"
                        output += f"• Average Daily Rate: ${result['average_daily_rate']:.2f}\n"
                        output += f"• Total Revenue: ${result['total_revenue']:.2f}\n"
                        output += f"• Number of Bookings: {result['bookings']}\n"
                        output += f"• Average Stay Length: {result['avg_stay_length']} days\n"
                        output += f"• Net Revenue: ${result['net_revenue']:.2f}\n\n"
                        
                        if "year_over_year" in result:
                            output += "Year-over-Year Performance:\n"
                            output += f"• Occupancy Rate Change: {result['year_over_year']['occupancy_rate_change']}%\n"
                            output += f"• Revenue Change: {result['year_over_year']['revenue_change']}%\n"
                        
                        return {"final_output": output}
                except Exception as e:
                    return {"final_output": f"Error processing property metrics: {str(e)}"}
            
            return {"final_output": "Please specify a valid property ID (e.g., P123)."}
            
        elif "compare property" in query or "comparison" in query:
            # Extract property ID from the query
            import re
            property_match = re.search(r'property\s+([\w\d]+)', query)
            if property_match:
                property_id = property_match.group(1).upper()
                # Call the appropriate tool
                try:
                    tool = agent.get_tool_by_name("get_property_comparisons")
                    if tool:
                        metric = None
                        if "occupancy" in query: metric = "occupancy_rate"
                        elif "rate" in query or "pricing" in query: metric = "average_daily_rate"
                        elif "revenue" in query: metric = "total_revenue"
                        
                        result = tool(property_id, metric)
                        
                        if "error" in result:
                            return {"final_output": result["error"]}
                            
                        # Format the response
                        output = f"Comparison data for property {property_id}:\n\n"
                        
                        comparison_data = result["comparison_data"]
                        if metric:
                            data = comparison_data[metric]
                            output += f"📊 {metric.replace('_', ' ').title()}:\n"
                            output += f"• Your property: {data['property_value']}{' %' if 'rate' in metric else ''}\n"
                            output += f"• Cohort average: {data['cohort_avg']}{' %' if 'rate' in metric else ''}\n"
                            output += f"• Percentile: {data['percentile']}\n"
                            output += f"• Summary: {data['comparison_summary']}\n"
                        else:
                            for key, data in comparison_data.items():
                                if key != "overall_ranking":
                                    output += f"📊 {key.replace('_', ' ').title()}:\n"
                                    output += f"• Your property: {data['property_value']}{' %' if 'rate' in key else ''}\n"
                                    output += f"• Cohort average: {data['cohort_avg']}{' %' if 'rate' in key else ''}\n"
                                    output += f"• Percentile: {data['percentile']}\n"
                                    output += f"• Summary: {data['comparison_summary']}\n\n"
                            
                            if "overall_ranking" in comparison_data:
                                output += "\n📈 Overall Ranking:\n"
                                output += f"• Position: #{comparison_data['overall_ranking']['position']} out of {comparison_data['overall_ranking']['total_properties']}\n"
                                output += f"• Percentile: {comparison_data['overall_ranking']['percentile']}\n"
                                output += f"• Summary: {comparison_data['overall_ranking']['summary']}\n"
                        
                        return {"final_output": output}
                except Exception as e:
                    return {"final_output": f"Error processing property comparisons: {str(e)}"}
            
            return {"final_output": "Please specify a valid property ID for comparison (e.g., P123)."}
        
        elif "recommend" in query or "improvements" in query or "improve" in query:
            # Extract property ID from the query
            import re
            property_match = re.search(r'property\s+([\w\d]+)', query)
            if property_match:
                property_id = property_match.group(1).upper()
                # Call the appropriate tool
                try:
                    tool = agent.get_tool_by_name("get_property_recommendations")
                    if tool:
                        result = tool(property_id)
                        
                        if "error" in result:
                            return {"final_output": result["error"]}
                            
                        # Format the response
                        output = f"Recommendations for property {property_id}:\n\n"
                        
                        if "pricing_recommendations" in result:
                            output += "💰 Pricing Recommendations:\n"
                            for rec in result["pricing_recommendations"]:
                                output += f"• {rec['recommendation']}\n"
                                output += f"  - Why: {rec['reasoning']}\n"
                                output += f"  - Potential impact: {rec['potential_impact']}\n\n"
                        
                        if "amenity_recommendations" in result:
                            output += "🛋️ Amenity Recommendations:\n"
                            for rec in result["amenity_recommendations"]:
                                output += f"• {rec['recommendation']}\n"
                                output += f"  - Why: {rec['reasoning']}\n"
                                output += f"  - Potential impact: {rec['potential_impact']}\n\n"
                        
                        if "operational_recommendations" in result:
                            output += "⚙️ Operational Recommendations:\n"
                            for rec in result["operational_recommendations"]:
                                output += f"• {rec['recommendation']}\n"
                                output += f"  - Why: {rec['reasoning']}\n"
                                output += f"  - Potential impact: {rec['potential_impact']}\n"
                        
                        return {"final_output": output}
                except Exception as e:
                    return {"final_output": f"Error processing property recommendations: {str(e)}"}
            
            return {"final_output": "Please specify a valid property ID for recommendations (e.g., P123)."}
        
        elif "outlier" in query or "unusual" in query or "anomaly" in query:
            # Extract property ID from the query
            import re
            property_match = re.search(r'property\s+([\w\d]+)', query)
            if property_match:
                property_id = property_match.group(1).upper()
                # Call the appropriate tool
                try:
                    tool = agent.get_tool_by_name("get_outlier_statistics")
                    if tool:
                        result = tool(property_id)
                        
                        if "error" in result:
                            return {"final_output": result["error"]}
                            
                        # Format the response
                        output = f"Outlier analysis for property {property_id}:\n\n"
                        
                        for key, data in result.items():
                            output += f"📊 {key.replace('_', ' ').title()}:\n"
                            if data["detected"]:
                                output += f"• Finding: Outlier detected\n"
                                output += f"• Details: {data['explanation']}\n\n"
                            else:
                                output += f"• Finding: No outliers detected\n"
                                output += f"• Details: {data['explanation']}\n\n"
                        
                        return {"final_output": output}
                except Exception as e:
                    return {"final_output": f"Error processing outlier statistics: {str(e)}"}
            
            return {"final_output": "Please specify a valid property ID for outlier analysis (e.g., P123)."}
        
        elif "cohort" in query or "group" in query:
            # Extract cohort ID from the query
            import re
            cohort_match = re.search(r'(beachfront|downtown|mountain|luxury)', query.lower())
            if cohort_match:
                cohort_id = cohort_match.group(1).lower()
                # Call the appropriate tool
                try:
                    tool = agent.get_tool_by_name("get_cohort_properties")
                    if tool:
                        result = tool(cohort_id)
                        
                        if "error" in result:
                            return {"final_output": result["error"]}
                            
                        # Format the response
                        output = f"Properties in the {result['name']} cohort:\n\n"
                        output += f"Description: {result['description']}\n\n"
                        
                        output += "Properties: \n"
                        for property_id in result["properties"]:
                            output += f"• {property_id}\n"
                        
                        output += "\nCohort Statistics:\n"
                        stats = result["summary_statistics"]
                        output += f"• Average Occupancy Rate: {stats['avg_occupancy_rate']}%\n"
                        output += f"• Average Daily Rate: ${stats['avg_daily_rate']}\n"
                        output += f"• Average Monthly Revenue: ${stats['avg_revenue_monthly']}\n"
                        output += f"• Total Properties: {stats['total_properties']}\n"
                        
                        return {"final_output": output}
                except Exception as e:
                    return {"final_output": f"Error processing cohort properties: {str(e)}"}
            
            return {"final_output": "Please specify a valid cohort (e.g., beachfront, downtown, mountain_view, luxury)."}
            
        # Default response for unrecognized queries
        return {
            "final_output": "I can help you with property performance metrics, comparisons, recommendations, and outlier detection. Please ask a specific question about a property (e.g., P123, P456, P789) or a cohort (beachfront, downtown, mountain_view, luxury)."
        }