# Phase 1: LLM Integration Foundation - Implementation Tasks (Hybrid Testing Strategy)

## Project Overview

**Objective**: Transform the Property Manager AI from a rule-based pattern matching system to an agentic LLM-powered system using a hybrid testing strategy that prevents API key handling conflicts and ensures reliable validation.

**Current State**: The system uses hardcoded regex patterns in `pm_ai/agents/__init__.py` (lines 44-249) to match specific query formats and manually extract parameters.

**Target State**: Replace rule-based matching with OpenAI GPT-4 integration that can understand natural language queries, maintain conversation context, and intelligently select appropriate tools.

**Testing Strategy**: Three-tier architecture to address API key handling conflicts:
- **Unit Tests (Mocked)**: Fast tests with mock OpenAI clients, no API costs
- **Integration Tests (Real API)**: Limited real API calls for critical validation (max 50 calls per run)
- **End-to-End Tests (Real API)**: Complete system validation with full conversation flows

**Timeline**: 2 weeks (Phase 1 of 4-phase project)

---

## Environment Setup

### Prerequisites
- Python 3.13+
- OpenAI API key with GPT-4 access
- Existing Property Manager AI codebase
- Poetry for dependency management

### Environment Configuration (Test-Aware)
Create separate environment files to prevent API key conflicts:

**Production Environment (.env):**
```bash
# Production/development API usage
OPENAI_API_KEY=your_production_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini  # or gpt-4
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.1
```

**Test Environment (.env.test):**
```bash
# Test-specific configuration
PM_AI_TEST_MODE=unit  # Options: unit, integration, e2e
OPENAI_API_KEY_TEST=your_test_openai_api_key_here  # Optional separate test key
PM_AI_DEBUG=true
PM_AI_MAX_API_CALLS_PER_TEST_RUN=50  # Cost control for integration tests
```

### Dependency Updates
Update `pyproject.toml` to include testing dependencies:
```toml
dependencies = [
    "openai (>=1.88.0,<2.0.0)",
    "openai-agents (>=0.0.19,<0.0.20)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "requests (>=2.31.0,<3.0.0)",
    "pydantic (>=2.0.0,<3.0.0)",
    "tiktoken (>=0.5.0,<1.0.0)",
    "pytest (>=7.0.0)",
    "pytest-mock (>=3.10.0)",
    "pytest-cov (>=4.0.0)",
    "pytest-env (>=0.8.0)",
    "pytest-xdist (>=3.0.0)",
]

[tool.pytest.ini_options]
testpaths = ["pm_ai/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests with mocked dependencies",
    "integration: Integration tests with real API calls",
    "e2e: End-to-end tests with full system",
    "slow: Tests that take longer than 30 seconds"
]
env = [
    "PM_AI_TEST_MODE=unit"
]

[tool.coverage.run]
source = ["pm_ai"]
omit = ["pm_ai/tests/*", "pm_ai/__pycache__/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]
```

---

## File Structure Overview

The following new files will be created in Phase 1 with hybrid testing strategy:

```
pm_ai/
├── intelligence/              # NEW: Core LLM intelligence modules
│   ├── __init__.py
│   ├── llm_engine.py         # OpenAI GPT-4 integration (test-aware)
│   ├── intent_parser.py      # Natural language intent recognition
│   └── context_manager.py    # Conversation context management
├── conversation/             # NEW: Conversation handling
│   ├── __init__.py
│   ├── session_manager.py    # Session state management
│   └── memory_store.py       # In-memory conversation storage
├── integration/              # NEW: Tool orchestration
│   ├── __init__.py
│   ├── tool_orchestrator.py  # Dynamic tool selection
│   └── result_synthesizer.py # Response formatting
├── config/                   # NEW: Test configuration system
│   ├── __init__.py
│   └── test_config.py        # Test environment management
├── tests/                    # ENHANCED: Three-tier testing
│   ├── conftest.py           # Pytest configuration and fixtures
│   ├── unit/                 # Unit tests with mocked dependencies
│   │   ├── __init__.py
│   │   ├── test_llm_engine.py
│   │   ├── test_intent_parser.py
│   │   └── test_context_manager.py
│   ├── integration/          # Integration tests with real API
│   │   ├── __init__.py
│   │   ├── test_llm_integration.py
│   │   └── test_intent_integration.py
│   └── e2e/                  # End-to-end system tests
│       ├── __init__.py
│       └── test_full_conversation.py
├── agents/                   # MODIFIED: Enhanced agent framework
│   ├── __init__.py           # Replace with LLM-powered version
│   └── property_manager.py   # Update with new agent definition
└── tools/                    # ENHANCED: Tool function improvements
    ├── enhanced_property_metrics.py    # NEW: Enhanced metrics tool
    ├── enhanced_property_comparisons.py # NEW: Enhanced comparisons
    └── property_resolver.py            # NEW: Property ID resolution
```

### Test Environment Files
```
project_root/
├── .env                      # Production environment variables
├── .env.test                 # Test-specific environment variables
└── pytest.ini               # Pytest configuration (optional)
```

---

## Task 1: Create Test-Aware LLM Engine Infrastructure

**Objective**: Establish the foundation for OpenAI GPT-4 integration with test-aware design that supports both mocked unit tests and real API integration tests, preventing API key handling conflicts.

**Files to Create**:
- `pm_ai/intelligence/llm_engine.py` (test-aware LLM engine)
- `pm_ai/config/test_config.py` (test environment management)
- `pm_ai/tests/conftest.py` (pytest fixtures)

**Dependencies**: None (foundational task)

**Estimated Time**: 6 hours

### Implementation Details

#### Step 1: Create Test Configuration System

First, create the test configuration system to manage environment separation:

```python
# pm_ai/config/test_config.py
"""
Test configuration management to prevent API key handling conflicts.
"""
import os
from typing import Optional

class TestConfig:
    """Test-specific configuration management."""

    @staticmethod
    def get_test_mode() -> str:
        """
        Determine test mode from environment.

        Returns:
            'unit' - Use mocks, no API calls
            'integration' - Use real API with test key
            'e2e' - Use real API with full system
        """
        return os.getenv("PM_AI_TEST_MODE", "unit")

    @staticmethod
    def get_openai_key() -> Optional[str]:
        """Get OpenAI API key based on test mode."""
        test_mode = TestConfig.get_test_mode()

        if test_mode == "unit":
            return None  # No API key needed for mocked tests
        elif test_mode in ["integration", "e2e"]:
            # Use test-specific key or fall back to main key
            return os.getenv("OPENAI_API_KEY_TEST") or os.getenv("OPENAI_API_KEY")

        return os.getenv("OPENAI_API_KEY")

    @staticmethod
    def should_use_real_api() -> bool:
        """Determine if tests should use real API calls."""
        return TestConfig.get_test_mode() in ["integration", "e2e"]

    @staticmethod
    def get_max_api_calls() -> int:
        """Get maximum API calls allowed per test run."""
        return int(os.getenv("PM_AI_MAX_API_CALLS_PER_TEST_RUN", "50"))
```

#### Step 2: Create Test-Aware LLM Engine

```python
# pm_ai/intelligence/llm_engine.py
"""
Test-aware LLM engine for OpenAI GPT-4 integration.
Supports both mocked unit tests and real API integration tests.
"""
import os
import time
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from openai import OpenAI
import tiktoken

@dataclass
class LLMConfig:
    """Configuration for LLM engine."""
    model: str = "gpt-4o-mini"
    max_tokens: int = 4096
    temperature: float = 0.1
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: int = 30
    test_mode: bool = False  # NEW: Enable test-specific behavior

class LLMEngine:
    """
    Test-aware LLM engine for OpenAI GPT-4 integration.

    Prevents API key handling conflicts by supporting:
    - Mock clients for unit tests (no API calls)
    - Real API clients for integration tests (limited calls)
    - Clear environment separation
    """

    def __init__(self, config: Optional[LLMConfig] = None, mock_client=None):
        self.config = config or LLMConfig()

        # Test-aware client initialization
        if mock_client:
            # Use provided mock client (for unit tests)
            self.client = mock_client
            self.is_mocked = True
        elif self.config.test_mode:
            # Test mode with real API but special handling
            api_key = self._get_test_api_key()
            self.client = OpenAI(api_key=api_key) if api_key else None
            self.is_mocked = False
        else:
            # Production mode
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            self.client = OpenAI(api_key=api_key)
            self.is_mocked = False

        # Initialize encoding if we have a real client
        if self.client and not self.is_mocked:
            try:
                self.encoding = tiktoken.encoding_for_model(self.config.model)
            except Exception:
                # Fallback for test environments
                self.encoding = tiktoken.get_encoding("cl100k_base")
        else:
            self.encoding = None

        self.logger = logging.getLogger(__name__)
        self.api_call_count = 0  # Track API calls for cost control

    def _get_test_api_key(self) -> Optional[str]:
        """Get API key for testing."""
        try:
            from ..config.test_config import TestConfig
            return TestConfig.get_openai_key()
        except ImportError:
            # Fallback if test_config not available
            return os.getenv("OPENAI_API_KEY_TEST") or os.getenv("OPENAI_API_KEY")

    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        if self.encoding:
            return len(self.encoding.encode(text))
        else:
            # Fallback estimation for mocked tests
            return len(text.split()) * 1.3  # Rough approximation

    def create_completion(
        self,
        messages: List[Dict[str, str]],
        functions: Optional[List[Dict]] = None,
        function_call: Optional[Union[str, Dict]] = None
    ) -> Dict[str, Any]:
        """
        Create completion with test-aware behavior.

        Args:
            messages: List of conversation messages
            functions: Available functions for function calling
            function_call: Function call specification

        Returns:
            Dict containing response and metadata
        """

        if not self.client:
            raise ValueError("No OpenAI client available (missing API key)")

        # For mocked clients, return test response
        if self.is_mocked:
            return self._create_mock_response(messages, functions, function_call)

        # Track API calls for cost control
        self.api_call_count += 1

        # Real API call logic
        for attempt in range(self.config.max_retries):
            try:
                request_params = {
                    "model": self.config.model,
                    "messages": messages,
                    "max_tokens": self.config.max_tokens,
                    "temperature": self.config.temperature,
                    "timeout": self.config.timeout
                }

                if functions:
                    request_params["tools"] = [{"type": "function", "function": f} for f in functions]
                if function_call:
                    if isinstance(function_call, str):
                        request_params["tool_choice"] = {"type": "function", "function": {"name": function_call}}
                    else:
                        request_params["tool_choice"] = function_call

                response = self.client.chat.completions.create(**request_params)
                return self._process_response(response, messages)

            except Exception as e:
                self.logger.warning(f"LLM API attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))

    def _create_mock_response(self, messages, functions, function_call) -> Dict[str, Any]:
        """Create mock response for testing."""
        # Default mock response
        mock_response = {
            "content": "Mock response for testing",
            "function_call": None,
            "finish_reason": "stop",
            "usage": {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        }

        # If function calling is expected, create mock function call
        if functions and function_call:
            if isinstance(function_call, dict) and "name" in function_call:
                function_name = function_call["name"]
            elif isinstance(function_call, str):
                function_name = function_call
            else:
                function_name = functions[0]["name"] if functions else "mock_function"

            mock_response["function_call"] = {
                "name": function_name,
                "arguments": {"mock": "response", "confidence": 0.95}
            }
            mock_response["content"] = None

        return mock_response

    def _process_response(self, response, messages: List[Dict]) -> Dict[str, Any]:
        """Process OpenAI API response and extract relevant information."""
        choice = response.choices[0]
        message = choice.message

        result = {
            "content": message.content,
            "function_call": None,
            "finish_reason": choice.finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }

        # Handle tool calls (new OpenAI API format)
        if hasattr(message, 'tool_calls') and message.tool_calls:
            tool_call = message.tool_calls[0]
            if tool_call.type == "function":
                result["function_call"] = {
                    "name": tool_call.function.name,
                    "arguments": json.loads(tool_call.function.arguments)
                }

        return result

    def get_api_call_count(self) -> int:
        """Get number of API calls made by this engine instance."""
        return self.api_call_count
```

#### Step 3: Create Pytest Fixtures

```python
# pm_ai/tests/conftest.py
"""
Pytest configuration and fixtures for hybrid testing strategy.
"""
import pytest
import os
from unittest.mock import Mock, MagicMock
from typing import Dict, Any

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig
from pm_ai.config.test_config import TestConfig

@pytest.fixture(scope="session")
def test_mode():
    """Determine test mode for the session."""
    return TestConfig.get_test_mode()

@pytest.fixture
def mock_openai_client():
    """Create a mock OpenAI client for unit tests."""
    mock_client = Mock()

    # Mock response structure
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = "Mock response content"
    mock_response.choices[0].message.tool_calls = None
    mock_response.choices[0].finish_reason = "stop"
    mock_response.usage.prompt_tokens = 10
    mock_response.usage.completion_tokens = 5
    mock_response.usage.total_tokens = 15

    mock_client.chat.completions.create.return_value = mock_response
    return mock_client

@pytest.fixture
def mock_llm_engine(mock_openai_client):
    """Create LLM engine with mocked client for unit tests."""
    config = LLMConfig(test_mode=True)
    return LLMEngine(config=config, mock_client=mock_openai_client)

@pytest.fixture
def real_llm_engine():
    """Create LLM engine with real API for integration tests."""
    if not TestConfig.should_use_real_api():
        pytest.skip("Real API tests disabled (set PM_AI_TEST_MODE=integration)")

    api_key = TestConfig.get_openai_key()
    if not api_key:
        pytest.skip("No OpenAI API key available for integration tests")

    config = LLMConfig(test_mode=True)
    return LLMEngine(config=config)

@pytest.fixture
def api_call_tracker():
    """Track API calls to enforce cost limits."""
    class APICallTracker:
        def __init__(self):
            self.call_count = 0
            self.max_calls = TestConfig.get_max_api_calls()

        def increment(self):
            self.call_count += 1
            if self.call_count > self.max_calls:
                pytest.fail(f"Exceeded maximum API calls ({self.max_calls}) for test run")

    return APICallTracker()
```

#### Step 4: Create Unit Tests (Mocked)

```python
# pm_ai/tests/unit/test_llm_engine.py
"""
Unit tests for LLM engine with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, patch
import os

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig

@pytest.mark.unit
class TestLLMEngineUnit:
    """Unit tests for LLM engine with mocked dependencies."""

    def test_initialization_with_mock(self, mock_llm_engine):
        """Test LLM engine initializes correctly with mock client."""
        assert mock_llm_engine.client is not None
        assert mock_llm_engine.is_mocked is True
        assert mock_llm_engine.config.test_mode is True

    def test_create_completion_mocked(self, mock_llm_engine):
        """Test completion creation with mocked client."""
        messages = [{"role": "user", "content": "Test message"}]

        result = mock_llm_engine.create_completion(messages)

        assert result["content"] == "Mock response for testing"
        assert result["usage"]["total_tokens"] == 15
        assert mock_llm_engine.client.chat.completions.create.called

    def test_function_calling_mock(self, mock_llm_engine):
        """Test function calling with mocked response."""
        messages = [{"role": "user", "content": "How is P123 performing?"}]
        functions = [{"name": "extract_intent", "description": "Extract intent"}]

        result = mock_llm_engine.create_completion(
            messages,
            functions,
            function_call="extract_intent"
        )

        # Verify function call was processed
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "extract_intent"

    def test_token_counting_fallback(self, mock_llm_engine):
        """Test token counting with fallback for mocked engine."""
        text = "Hello world test"
        count = mock_llm_engine.count_tokens(text)

        # Should use fallback estimation
        assert count > 0
        assert isinstance(count, (int, float))

    def test_api_call_tracking(self, mock_llm_engine):
        """Test API call counting for cost control."""
        initial_count = mock_llm_engine.get_api_call_count()

        messages = [{"role": "user", "content": "Test"}]
        mock_llm_engine.create_completion(messages)

        # Mocked calls should not increment counter
        assert mock_llm_engine.get_api_call_count() == initial_count
```

#### Step 5: Create Integration Tests (Real API)

```python
# pm_ai/tests/integration/test_llm_integration.py
"""
Integration tests using real OpenAI API with cost controls.
"""
import pytest
import os

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig

@pytest.mark.integration
class TestLLMIntegration:
    """Integration tests using real OpenAI API."""

    def test_real_api_completion(self, real_llm_engine, api_call_tracker):
        """Test actual API completion with cost tracking."""
        api_call_tracker.increment()

        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say 'Hello, World!' and nothing else."}
        ]

        result = real_llm_engine.create_completion(messages)

        assert result["content"] is not None
        assert "hello" in result["content"].lower()
        assert result["usage"]["total_tokens"] > 0
        assert real_llm_engine.get_api_call_count() == 1

    def test_function_calling_real_api(self, real_llm_engine, api_call_tracker):
        """Test function calling with real API."""
        api_call_tracker.increment()

        messages = [{"role": "user", "content": "Extract intent from: How is P123 performing?"}]
        functions = [{
            "name": "extract_intent",
            "description": "Extract intent from property query",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {"type": "string"},
                    "property_id": {"type": "string"}
                }
            }
        }]

        result = real_llm_engine.create_completion(
            messages,
            functions,
            function_call="extract_intent"
        )

        # Should get a function call response
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "extract_intent"

    @pytest.mark.slow
    def test_retry_logic_real_api(self, real_llm_engine):
        """Test retry logic with real API (marked as slow)."""
        # This test may be slow due to retry delays
        # Only run in comprehensive test suites
        pass
```

### Acceptance Criteria

#### Unit Test Acceptance Criteria
- [ ] LLMEngine initializes correctly with mock client
- [ ] Mock responses are generated without API calls
- [ ] Function calling works with mocked responses
- [ ] Token counting fallback works for mocked tests
- [ ] All unit tests execute in under 30 seconds
- [ ] No real API calls are made during unit tests

#### Integration Test Acceptance Criteria
- [ ] LLMEngine works with real OpenAI API
- [ ] API call tracking prevents cost overruns
- [ ] Function calling works with real API responses
- [ ] Integration tests respect API call limits (max 50 per run)
- [ ] Real API tests pass consistently (>95% success rate)
- [ ] Environment separation prevents test/production conflicts

### Testing Commands

```bash
# Unit tests only (fast, no API calls)
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/test_llm_engine.py -v

# Integration tests (real API calls, requires key)
PM_AI_TEST_MODE=integration pytest pm_ai/tests/integration/test_llm_integration.py -v

# All tests with coverage
PM_AI_TEST_MODE=unit pytest pm_ai/tests/ --cov=pm_ai.intelligence --cov-report=html

# Test with API call limit enforcement
PM_AI_TEST_MODE=integration PM_AI_MAX_API_CALLS_PER_TEST_RUN=10 pytest pm_ai/tests/integration/ -v
```

### Error Recovery
- **Missing API Key**: Check .env and .env.test files, ensure OPENAI_API_KEY is set
- **API Key Conflicts**: Verify PM_AI_TEST_MODE is set correctly for test type
- **Import Errors**: Run `poetry install` to install testing dependencies
- **API Failures**: Check OpenAI service status and API key validity
- **Cost Overruns**: Reduce PM_AI_MAX_API_CALLS_PER_TEST_RUN or use unit tests only
- **Mock Failures**: Verify pytest fixtures are properly configured in conftest.py

---

## Task 2: Implement Intent Recognition System

**Objective**: Create a natural language intent parser that can understand user queries and determine appropriate actions without hardcoded patterns.

**Files to Create**: `pm_ai/intelligence/intent_parser.py`

**Dependencies**: Task 1 (LLM Engine)

**Estimated Time**: 6 hours

### Implementation Details

The intent parser will use GPT-4 to understand user queries and extract structured information:

```python
# pm_ai/intelligence/intent_parser.py
"""
Natural language intent recognition for property management queries.
Replaces hardcoded pattern matching with LLM-powered understanding.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json

from .llm_engine import LLMEngine

class QueryIntent(Enum):
    """Possible user intents for property management queries."""
    PROPERTY_PERFORMANCE = "property_performance"
    PROPERTY_COMPARISON = "property_comparison"
    RECOMMENDATIONS = "recommendations"
    OUTLIER_ANALYSIS = "outlier_analysis"
    COHORT_ANALYSIS = "cohort_analysis"
    GENERAL_INQUIRY = "general_inquiry"
    CLARIFICATION_NEEDED = "clarification_needed"

@dataclass
class ParsedIntent:
    """Structured representation of parsed user intent."""
    intent: QueryIntent
    confidence: float
    parameters: Dict[str, Any]
    clarification_needed: Optional[str] = None
    suggested_actions: List[str] = None

class IntentParser:
    """
    Natural language intent parser for property management queries.
    
    Uses GPT-4 to understand user intent and extract parameters without
    relying on hardcoded patterns or regex matching.
    """
    
    def __init__(self, llm_engine: LLMEngine):
        self.llm_engine = llm_engine
        self.system_prompt = self._create_system_prompt()
    
    def parse_query(self, query: str, context: Optional[Dict] = None) -> ParsedIntent:
        """
        Parse user query to extract intent and parameters.
        
        Args:
            query: User's natural language query
            context: Optional conversation context
            
        Returns:
            ParsedIntent with structured information
        """
        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": self._format_query(query, context)}
        ]
        
        functions = [self._get_intent_extraction_function()]
        
        response = self.llm_engine.create_completion(
            messages=messages,
            functions=functions,
            function_call={"name": "extract_intent"}
        )
        
        if response["function_call"]:
            return self._process_intent_response(response["function_call"]["arguments"])
        else:
            # Fallback if function calling fails
            return ParsedIntent(
                intent=QueryIntent.GENERAL_INQUIRY,
                confidence=0.5,
                parameters={},
                clarification_needed="I couldn't understand your request. Could you please rephrase?"
            )
    
    def _create_system_prompt(self) -> str:
        """Create system prompt for intent recognition."""
        return """You are an expert at understanding property management queries.

Your task is to analyze user queries about vacation rental properties and extract:
1. The user's intent (what they want to accomplish)
2. Relevant parameters (property IDs, time periods, metrics, etc.)
3. Confidence level in your interpretation
4. Whether clarification is needed

Property Context:
- Properties are identified by IDs like P123, P456, P789
- Cohorts include: beachfront, downtown, mountain_view, luxury
- Time periods can be: last_30_days, last_quarter, last_year, or custom ranges
- Common metrics: occupancy_rate, average_daily_rate, total_revenue

Handle natural language variations like:
- "How's my beachfront place doing?" → property_performance for beachfront cohort
- "Compare P123 to similar properties" → property_comparison for P123
- "Any recommendations for the downtown rental?" → recommendations for downtown cohort
- "What's unusual about P456?" → outlier_analysis for P456

Always extract structured parameters and indicate confidence level."""
    
    def _format_query(self, query: str, context: Optional[Dict]) -> str:
        """Format query with context for LLM processing."""
        formatted = f"User Query: {query}"
        
        if context:
            formatted += f"\n\nContext: {json.dumps(context, indent=2)}"
        
        return formatted
    
    def _get_intent_extraction_function(self) -> Dict:
        """Define function schema for intent extraction."""
        return {
            "name": "extract_intent",
            "description": "Extract intent and parameters from property management query",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {
                        "type": "string",
                        "enum": [intent.value for intent in QueryIntent],
                        "description": "Primary intent of the user query"
                    },
                    "confidence": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "description": "Confidence level in intent recognition (0.0-1.0)"
                    },
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "property_ids": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific property IDs mentioned (e.g., P123, P456)"
                            },
                            "cohort": {
                                "type": "string",
                                "enum": ["beachfront", "downtown", "mountain_view", "luxury"],
                                "description": "Property cohort/group mentioned"
                            },
                            "time_period": {
                                "type": "string",
                                "description": "Time period for analysis (e.g., last_30_days, this_month)"
                            },
                            "metrics": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific metrics mentioned (occupancy, revenue, etc.)"
                            },
                            "comparison_type": {
                                "type": "string",
                                "description": "Type of comparison requested (vs cohort, vs previous period)"
                            }
                        }
                    },
                    "clarification_needed": {
                        "type": "string",
                        "description": "Question to ask user if query is ambiguous"
                    },
                    "suggested_actions": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Suggested follow-up actions or tools to use"
                    }
                },
                "required": ["intent", "confidence", "parameters"]
            }
        }
    
    def _process_intent_response(self, arguments: Dict) -> ParsedIntent:
        """Process function call response into ParsedIntent object."""
        return ParsedIntent(
            intent=QueryIntent(arguments["intent"]),
            confidence=arguments["confidence"],
            parameters=arguments["parameters"],
            clarification_needed=arguments.get("clarification_needed"),
            suggested_actions=arguments.get("suggested_actions", [])
        )
```

### Acceptance Criteria
- [ ] IntentParser correctly identifies all QueryIntent types
- [ ] Natural language variations are handled (e.g., "my beachfront place" → beachfront cohort)
- [ ] Parameters are extracted accurately (property IDs, time periods, metrics)
- [ ] Confidence scoring works appropriately
- [ ] Clarification requests are generated for ambiguous queries
- [ ] Function calling integration works with LLM engine

### Testing Commands
```bash
# Test intent parsing
python -c "
from pm_ai.intelligence.llm_engine import LLMEngine
from pm_ai.intelligence.intent_parser import IntentParser

engine = LLMEngine()
parser = IntentParser(engine)

# Test various query types
queries = [
    'How is property P123 performing this month?',
    'Compare my beachfront place to similar properties',
    'Any recommendations for the downtown rental?',
    'What\\'s unusual about P456 lately?'
]

for query in queries:
    result = parser.parse_query(query)
    print(f'Query: {query}')
    print(f'Intent: {result.intent}, Confidence: {result.confidence}')
    print(f'Parameters: {result.parameters}')
    print('---')
"
```

---

## Task 3: Create Conversation Context Management

**Objective**: Implement conversation memory and context management to enable multi-turn conversations and reference resolution.

**Files to Create**: 
- `pm_ai/intelligence/context_manager.py`
- `pm_ai/conversation/session_manager.py`
- `pm_ai/conversation/memory_store.py`

**Dependencies**: Task 1 (LLM Engine), Task 2 (Intent Parser)

**Estimated Time**: 8 hours

### Implementation Details

Create a comprehensive context management system that maintains conversation state and enables reference resolution.

First, create the memory store:

```python
# pm_ai/conversation/memory_store.py
"""
In-memory conversation storage for maintaining context across turns.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid

@dataclass
class ConversationTurn:
    """Single turn in a conversation."""
    turn_id: str
    timestamp: datetime
    user_query: str
    parsed_intent: Dict[str, Any]
    system_response: str
    tools_used: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConversationSession:
    """Complete conversation session with multiple turns."""
    session_id: str
    user_id: Optional[str]
    start_time: datetime
    last_activity: datetime
    turns: List[ConversationTurn] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)

class MemoryStore:
    """
    In-memory storage for conversation sessions and context.
    
    In production, this would be replaced with persistent storage
    (Redis, PostgreSQL, etc.) but for Phase 1, in-memory is sufficient.
    """
    
    def __init__(self):
        self.sessions: Dict[str, ConversationSession] = {}
        self.max_sessions = 100  # Prevent memory bloat
        self.max_turns_per_session = 50
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create new conversation session."""
        session_id = str(uuid.uuid4())
        now = datetime.now()
        
        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            start_time=now,
            last_activity=now
        )
        
        self.sessions[session_id] = session
        self._cleanup_old_sessions()
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """Retrieve conversation session."""
        return self.sessions.get(session_id)
    
    def add_turn(
        self,
        session_id: str,
        user_query: str,
        parsed_intent: Dict[str, Any],
        system_response: str,
        tools_used: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Add new turn to conversation session."""
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
        
        turn_id = str(uuid.uuid4())
        turn = ConversationTurn(
            turn_id=turn_id,
            timestamp=datetime.now(),
            user_query=user_query,
            parsed_intent=parsed_intent,
            system_response=system_response,
            tools_used=tools_used or [],
            metadata=metadata or {}
        )
        
        session.turns.append(turn)
        session.last_activity = datetime.now()
        
        # Limit turns per session
        if len(session.turns) > self.max_turns_per_session:
            session.turns = session.turns[-self.max_turns_per_session:]
        
        return turn_id
    
    def update_context(self, session_id: str, context_updates: Dict[str, Any]):
        """Update session context."""
        session = self.sessions.get(session_id)
        if session:
            session.context.update(context_updates)
            session.last_activity = datetime.now()
    
    def get_recent_turns(self, session_id: str, count: int = 5) -> List[ConversationTurn]:
        """Get recent conversation turns."""
        session = self.sessions.get(session_id)
        if not session:
            return []
        
        return session.turns[-count:] if session.turns else []
    
    def _cleanup_old_sessions(self):
        """Remove oldest sessions if limit exceeded."""
        if len(self.sessions) > self.max_sessions:
            # Sort by last activity and remove oldest
            sorted_sessions = sorted(
                self.sessions.items(),
                key=lambda x: x[1].last_activity
            )
            
            sessions_to_remove = len(self.sessions) - self.max_sessions
            for session_id, _ in sorted_sessions[:sessions_to_remove]:
                del self.sessions[session_id]
```

### Acceptance Criteria
- [ ] Memory store creates and manages conversation sessions
- [ ] Conversation turns are stored with complete metadata
- [ ] Session cleanup prevents memory bloat
- [ ] Context updates work correctly
- [ ] Recent turns retrieval functions properly

### Testing Commands
```bash
# Test memory store
python -c "
from pm_ai.conversation.memory_store import MemoryStore

store = MemoryStore()
session_id = store.create_session('test_user')
print(f'Created session: {session_id}')

# Add a conversation turn
store.add_turn(
    session_id,
    'How is P123 performing?',
    {'intent': 'property_performance', 'property_id': 'P123'},
    'Property P123 is performing well...',
    ['get_property_metrics']
)

session = store.get_session(session_id)
print(f'Session has {len(session.turns)} turns')
print('Memory store working correctly')
"
```

---

## Task 4: Implement Tool Orchestration System

**Objective**: Create intelligent tool selection and execution system that can dynamically choose appropriate tools based on parsed intent.

**Files to Create**:
- `pm_ai/integration/tool_orchestrator.py`
- `pm_ai/integration/result_synthesizer.py`

**Dependencies**: Task 1 (LLM Engine), Task 2 (Intent Parser), Task 3 (Context Management)

**Estimated Time**: 6 hours

### Implementation Details

Create a tool orchestrator that can intelligently select and execute tools:

```python
# pm_ai/integration/tool_orchestrator.py
"""
Dynamic tool selection and execution orchestration.
Replaces hardcoded tool mapping with intelligent selection.
"""
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import logging

from ..intelligence.intent_parser import ParsedIntent, QueryIntent
from ..intelligence.llm_engine import LLMEngine

@dataclass
class ToolResult:
    """Result from tool execution."""
    tool_name: str
    success: bool
    data: Any
    error_message: Optional[str] = None
    execution_time: float = 0.0

class ToolOrchestrator:
    """
    Intelligent tool selection and execution orchestration.

    Determines which tools to use based on parsed intent and executes
    them in the appropriate order with proper error handling.
    """

    def __init__(self, llm_engine: LLMEngine):
        self.llm_engine = llm_engine
        self.available_tools: Dict[str, Callable] = {}
        self.tool_descriptions: Dict[str, str] = {}
        self.logger = logging.getLogger(__name__)

    def register_tool(self, name: str, function: Callable, description: str):
        """Register a tool function with the orchestrator."""
        self.available_tools[name] = function
        self.tool_descriptions[name] = description

    def execute_intent(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> List[ToolResult]:
        """
        Execute tools based on parsed intent.

        Args:
            parsed_intent: Structured intent from intent parser
            context: Conversation context

        Returns:
            List of tool execution results
        """
        # Determine which tools to use
        selected_tools = self._select_tools(parsed_intent, context)

        # Execute tools in sequence
        results = []
        for tool_config in selected_tools:
            result = self._execute_tool(tool_config)
            results.append(result)

            # Stop execution if critical tool fails
            if not result.success and tool_config.get("critical", False):
                self.logger.error(f"Critical tool {result.tool_name} failed: {result.error_message}")
                break

        return results

    def _select_tools(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select appropriate tools based on intent and context."""
        tools = []

        if parsed_intent.intent == QueryIntent.PROPERTY_PERFORMANCE:
            tools.append({
                "name": "get_property_metrics",
                "parameters": self._extract_metrics_parameters(parsed_intent),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.PROPERTY_COMPARISON:
            tools.append({
                "name": "get_property_comparisons",
                "parameters": self._extract_comparison_parameters(parsed_intent),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.RECOMMENDATIONS:
            tools.append({
                "name": "get_property_recommendations",
                "parameters": self._extract_recommendation_parameters(parsed_intent),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.OUTLIER_ANALYSIS:
            tools.append({
                "name": "get_outlier_statistics",
                "parameters": self._extract_outlier_parameters(parsed_intent),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.COHORT_ANALYSIS:
            tools.append({
                "name": "get_cohort_properties",
                "parameters": self._extract_cohort_parameters(parsed_intent),
                "critical": True
            })

        return tools

    def _execute_tool(self, tool_config: Dict[str, Any]) -> ToolResult:
        """Execute a single tool with error handling."""
        tool_name = tool_config["name"]
        parameters = tool_config["parameters"]

        if tool_name not in self.available_tools:
            return ToolResult(
                tool_name=tool_name,
                success=False,
                data=None,
                error_message=f"Tool {tool_name} not found"
            )

        try:
            import time
            start_time = time.time()

            tool_function = self.available_tools[tool_name]
            result = tool_function(**parameters)

            execution_time = time.time() - start_time

            # Check if result indicates an error
            if isinstance(result, dict) and "error" in result:
                return ToolResult(
                    tool_name=tool_name,
                    success=False,
                    data=result,
                    error_message=result["error"],
                    execution_time=execution_time
                )

            return ToolResult(
                tool_name=tool_name,
                success=True,
                data=result,
                execution_time=execution_time
            )

        except Exception as e:
            self.logger.exception(f"Tool {tool_name} execution failed")
            return ToolResult(
                tool_name=tool_name,
                success=False,
                data=None,
                error_message=str(e)
            )

    def _extract_metrics_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for property metrics tool."""
        params = {}

        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]

        # Extract time period
        if "time_period" in parsed_intent.parameters:
            params["period"] = self._normalize_time_period(parsed_intent.parameters["time_period"])
        else:
            params["period"] = "last_30_days"  # Default

        return params

    def _extract_comparison_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for property comparison tool."""
        params = {}

        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]

        # Extract specific metric if mentioned
        if "metrics" in parsed_intent.parameters and parsed_intent.parameters["metrics"]:
            metric_mapping = {
                "occupancy": "occupancy_rate",
                "rate": "average_daily_rate",
                "revenue": "total_revenue"
            }
            for metric in parsed_intent.parameters["metrics"]:
                if metric.lower() in metric_mapping:
                    params["metric"] = metric_mapping[metric.lower()]
                    break

        return params

    def _extract_recommendation_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for recommendations tool."""
        params = {}

        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]

        return params

    def _extract_outlier_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for outlier analysis tool."""
        params = {}

        # Extract property ID
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]

        return params

    def _extract_cohort_parameters(self, parsed_intent: ParsedIntent) -> Dict[str, Any]:
        """Extract parameters for cohort analysis tool."""
        params = {}

        # Extract cohort name
        if "cohort" in parsed_intent.parameters:
            params["cohort_id"] = parsed_intent.parameters["cohort"]

        return params

    def _normalize_time_period(self, time_period: str) -> str:
        """Normalize natural language time periods to standard formats."""
        period_mapping = {
            "this month": "last_30_days",
            "last month": "last_30_days",
            "month": "last_30_days",
            "quarter": "last_quarter",
            "this quarter": "last_quarter",
            "year": "last_year",
            "this year": "last_year",
            "lately": "last_30_days",
            "recently": "last_30_days"
        }

        return period_mapping.get(time_period.lower(), "last_30_days")
```

### Acceptance Criteria
- [ ] Tool orchestrator correctly maps intents to tools
- [ ] Parameter extraction works for all tool types
- [ ] Error handling prevents system crashes
- [ ] Tool registration system works properly
- [ ] Time period normalization handles natural language
- [ ] Critical tool failure stops execution appropriately

---

## Task 5: Create Enhanced Agent Framework

**Objective**: Replace the hardcoded rule-based agent system with an LLM-powered agentic framework that integrates all the new components.

**Files to Modify**:
- `pm_ai/agents/__init__.py` (complete replacement)
- `pm_ai/agents/property_manager.py` (update agent definition)

**Dependencies**: Tasks 1-4 (All previous components)

**Estimated Time**: 6 hours

### Implementation Details

Replace the existing rule-based agent with an LLM-powered version:

```python
# pm_ai/agents/__init__.py (COMPLETE REPLACEMENT)
"""
Enhanced agentic framework with LLM-powered intelligence.
Replaces hardcoded pattern matching with natural language understanding.
"""
import os
import logging
from typing import Dict, List, Callable, Optional, Any, TypedDict

from ..intelligence.llm_engine import LLMEngine, LLMConfig
from ..intelligence.intent_parser import IntentParser
from ..intelligence.context_manager import ContextManager
from ..conversation.session_manager import SessionManager
from ..conversation.memory_store import MemoryStore
from ..integration.tool_orchestrator import ToolOrchestrator
from ..integration.result_synthesizer import ResultSynthesizer

class CompletionResult(TypedDict):
    """Result from agent completion."""
    final_output: str
    session_id: str
    tools_used: List[str]
    confidence: float

class AgenticAgent:
    """
    Enhanced agentic framework with LLM-powered intelligence.

    Replaces rule-based pattern matching with:
    - Natural language understanding via GPT-4
    - Conversation context and memory
    - Intelligent tool selection and orchestration
    - Autonomous reasoning and response generation
    """

    def __init__(self, name: str, instructions: str, tools: List[Callable]):
        self.name = name
        self.instructions = instructions
        self.tools = tools

        # Initialize core components
        self.llm_engine = LLMEngine(LLMConfig())
        self.intent_parser = IntentParser(self.llm_engine)
        self.memory_store = MemoryStore()
        self.session_manager = SessionManager(self.memory_store)
        self.tool_orchestrator = ToolOrchestrator(self.llm_engine)
        self.result_synthesizer = ResultSynthesizer(self.llm_engine)

        # Register tools with orchestrator
        self._register_tools()

        self.logger = logging.getLogger(__name__)

    def _register_tools(self):
        """Register all available tools with the orchestrator."""
        for tool in self.tools:
            tool_name = tool.__name__
            tool_doc = tool.__doc__ or f"Tool function: {tool_name}"
            self.tool_orchestrator.register_tool(tool_name, tool, tool_doc)

    def get_tool_by_name(self, tool_name: str) -> Optional[Callable]:
        """Get tool by name (for backward compatibility)."""
        return self.tool_orchestrator.available_tools.get(tool_name)

class AgenticRunner:
    """
    Enhanced runner for agentic interactions.

    Processes queries through the complete agentic pipeline:
    1. Parse intent from natural language
    2. Maintain conversation context
    3. Select and execute appropriate tools
    4. Synthesize intelligent responses
    """

    @staticmethod
    def run_sync(agent: AgenticAgent, query: str, session_id: Optional[str] = None) -> CompletionResult:
        """
        Process query through the agentic pipeline.

        Args:
            agent: AgenticAgent instance
            query: User's natural language query
            session_id: Optional session ID for conversation continuity

        Returns:
            CompletionResult with response and metadata
        """
        try:
            # Get or create session
            if not session_id:
                session_id = agent.session_manager.create_session()

            # Get conversation context
            context = agent.session_manager.get_context(session_id)

            # Parse user intent
            parsed_intent = agent.intent_parser.parse_query(query, context)

            # Check if clarification is needed
            if parsed_intent.clarification_needed:
                return CompletionResult(
                    final_output=parsed_intent.clarification_needed,
                    session_id=session_id,
                    tools_used=[],
                    confidence=parsed_intent.confidence
                )

            # Execute appropriate tools
            tool_results = agent.tool_orchestrator.execute_intent(parsed_intent, context)

            # Synthesize response
            response = agent.result_synthesizer.synthesize_response(
                query=query,
                parsed_intent=parsed_intent,
                tool_results=tool_results,
                context=context
            )

            # Update conversation memory
            agent.session_manager.add_turn(
                session_id=session_id,
                user_query=query,
                parsed_intent=parsed_intent.__dict__,
                system_response=response,
                tools_used=[r.tool_name for r in tool_results]
            )

            return CompletionResult(
                final_output=response,
                session_id=session_id,
                tools_used=[r.tool_name for r in tool_results],
                confidence=parsed_intent.confidence
            )

        except Exception as e:
            agent.logger.exception(f"Error processing query: {query}")
            return CompletionResult(
                final_output=f"I encountered an error processing your request: {str(e)}",
                session_id=session_id or "error",
                tools_used=[],
                confidence=0.0
            )

# Backward compatibility aliases
Agent = AgenticAgent
Runner = AgenticRunner
```

### Acceptance Criteria
- [ ] AgenticAgent initializes all components correctly
- [ ] Tool registration works with existing tool functions
- [ ] AgenticRunner processes queries through complete pipeline
- [ ] Error handling prevents system crashes
- [ ] Backward compatibility maintained for existing code
- [ ] Session management integrates properly
- [ ] Response synthesis produces coherent outputs

---

## Task 6: Update Main Application Entry Point

**Objective**: Update the main application to use the new agentic framework while maintaining the same CLI interface.

**Files to Modify**: `pm_ai/main.py`

**Dependencies**: Task 5 (Enhanced Agent Framework)

**Estimated Time**: 2 hours

### Implementation Details

Update the main application to use the new agentic system:

```python
# pm_ai/main.py (MODIFICATIONS)
#!/usr/bin/env python
"""
PM_AI - Property Manager AI Assistant (Enhanced Agentic Version)

This is the main entry point for the Property Manager AI application.
Now powered by LLM-based natural language understanding.
"""
import os
from dotenv import load_dotenv
import sys
import os
import logging

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pm_ai.agents import AgenticRunner as Runner
from pm_ai.agents.property_manager import create_agent

def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pm_ai.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """
    Run the Property Manager AI assistant with interactive prompts.
    Now with enhanced agentic capabilities.
    """
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    # Load environment variables
    load_dotenv()

    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY not found in environment variables.")
        print("Please add it to your .env file or export it directly.")
        print("Example: export OPENAI_API_KEY='your-api-key-here'")
        return

    # Create the enhanced agentic agent
    try:
        agent = create_agent()
        logger.info("Agentic Property Manager AI initialized successfully")
    except Exception as e:
        print(f"Error initializing agent: {e}")
        logger.error(f"Agent initialization failed: {e}")
        return

    # Welcome message
    print("\n" + "="*70)
    print("Welcome to Property Manager AI - Enhanced Agentic Assistant")
    print("="*70)
    print("I can now understand natural language and maintain conversation context!")
    print("\nExample questions you can ask:")
    print("- How's my beachfront property doing lately?")
    print("- Compare P123 to similar properties in the area")
    print("- What improvements would you recommend for the downtown rental?")
    print("- Show me all properties in the luxury cohort")
    print("- Is there anything unusual about P456's performance?")
    print("- How did my properties perform last quarter?")
    print("\nType 'exit' or 'quit' to end the session.")
    print("="*70 + "\n")

    # Initialize session
    session_id = None

    # Interactive loop
    while True:
        # Get user input
        try:
            user_input = input("\nYou: ").strip()
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break

        # Check for exit command
        if user_input.lower() in ["exit", "quit", "bye", "goodbye"]:
            print("\nThank you for using Property Manager AI. Goodbye!")
            break

        if not user_input:
            continue

        # Process the query with enhanced agentic capabilities
        print("\nProperty Manager AI: ", end="", flush=True)
        try:
            result = Runner.run_sync(agent, user_input, session_id)

            # Update session ID for conversation continuity
            session_id = result["session_id"]

            # Display response
            print(result["final_output"])

            # Show confidence and tools used (optional debug info)
            if os.getenv("PM_AI_DEBUG", "").lower() == "true":
                print(f"\n[Debug] Confidence: {result['confidence']:.2f}, Tools: {result['tools_used']}")

        except Exception as e:
            logger.exception("Error processing user query")
            print(f"Sorry, I encountered an error: {e}")
            print("Please try rephrasing your question or contact support if the issue persists.")

if __name__ == "__main__":
    main()
```

### Acceptance Criteria
- [ ] Main application starts with new agentic framework
- [ ] CLI interface remains familiar to users
- [ ] Session continuity works across conversation turns
- [ ] Error handling provides helpful messages
- [ ] Debug mode shows confidence and tools used
- [ ] Logging captures important events
- [ ] Environment variable validation works

### Testing Commands
```bash
# Test the enhanced application
cd /path/to/project
python -m pm_ai.main

# Test with debug mode
PM_AI_DEBUG=true python -m pm_ai.main

# Test environment validation
unset OPENAI_API_KEY
python -m pm_ai.main  # Should show error message
```

---

## Task 7: Create Comprehensive Test Suite

**Objective**: Create tests for all new components to ensure reliability and catch regressions.

**Files to Create**:
- `pm_ai/tests/test_llm_engine.py`
- `pm_ai/tests/test_intent_parser.py`
- `pm_ai/tests/test_agentic_framework.py`

**Dependencies**: All previous tasks

**Estimated Time**: 4 hours

### Implementation Details

Create comprehensive tests for the new agentic system:

```python
# pm_ai/tests/test_agentic_framework.py
"""
Comprehensive tests for the enhanced agentic framework.
"""
import pytest
import os
from unittest.mock import Mock, patch

from pm_ai.agents import AgenticAgent, AgenticRunner
from pm_ai.intelligence.intent_parser import QueryIntent
from pm_ai.tools.property_metrics import get_property_metrics
from pm_ai.tools.property_comparisons import get_property_comparisons

class TestAgenticFramework:
    """Test the complete agentic framework integration."""

    @pytest.fixture
    def mock_agent(self):
        """Create a mock agent for testing."""
        tools = [get_property_metrics, get_property_comparisons]
        agent = AgenticAgent(
            name="TestAgent",
            instructions="Test agent for property management",
            tools=tools
        )
        return agent

    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_agent_initialization(self, mock_agent):
        """Test that agent initializes correctly."""
        assert mock_agent.name == "TestAgent"
        assert len(mock_agent.tools) == 2
        assert mock_agent.llm_engine is not None
        assert mock_agent.intent_parser is not None
        assert mock_agent.tool_orchestrator is not None

    @patch('pm_ai.intelligence.llm_engine.OpenAI')
    def test_natural_language_query_processing(self, mock_openai, mock_agent):
        """Test processing of natural language queries."""
        # Mock OpenAI response for intent parsing
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.function_call.name = "extract_intent"
        mock_response.choices[0].message.function_call.arguments = '''
        {
            "intent": "property_performance",
            "confidence": 0.95,
            "parameters": {
                "property_ids": ["P123"],
                "time_period": "last_30_days"
            }
        }
        '''
        mock_response.usage.prompt_tokens = 100
        mock_response.usage.completion_tokens = 50
        mock_response.usage.total_tokens = 150

        mock_openai.return_value.chat.completions.create.return_value = mock_response

        # Test query processing
        result = AgenticRunner.run_sync(
            mock_agent,
            "How is my property P123 performing this month?"
        )

        assert "final_output" in result
        assert result["confidence"] > 0.8
        assert len(result["tools_used"]) > 0

    def test_conversation_continuity(self, mock_agent):
        """Test that conversation context is maintained."""
        # First query
        result1 = AgenticRunner.run_sync(mock_agent, "How is P123 doing?")
        session_id = result1["session_id"]

        # Follow-up query using same session
        result2 = AgenticRunner.run_sync(
            mock_agent,
            "What about compared to similar properties?",
            session_id
        )

        assert result2["session_id"] == session_id
        # Context should help resolve "similar properties" reference

    def test_error_handling(self, mock_agent):
        """Test error handling for various failure scenarios."""
        # Test with invalid query
        result = AgenticRunner.run_sync(mock_agent, "")
        assert "error" in result["final_output"].lower() or "help" in result["final_output"].lower()

        # Test with malformed input
        result = AgenticRunner.run_sync(mock_agent, "!@#$%^&*()")
        assert result["confidence"] < 0.8  # Should have low confidence

    def test_tool_integration(self, mock_agent):
        """Test that existing tools work with new framework."""
        # Verify tools are registered
        assert "get_property_metrics" in mock_agent.tool_orchestrator.available_tools
        assert "get_property_comparisons" in mock_agent.tool_orchestrator.available_tools

        # Test tool execution
        tool = mock_agent.get_tool_by_name("get_property_metrics")
        assert tool is not None

        # Test tool result
        result = tool("P123", "last_30_days")
        assert isinstance(result, dict)

if __name__ == "__main__":
    pytest.main([__file__])
```

### Acceptance Criteria
- [ ] All tests pass successfully
- [ ] Agent initialization is tested
- [ ] Natural language processing is verified
- [ ] Conversation continuity is validated
- [ ] Error handling scenarios are covered
- [ ] Tool integration works correctly
- [ ] Mock objects prevent actual API calls during testing

### Testing Commands
```bash
# Run all tests
cd /path/to/project
python -m pytest pm_ai/tests/ -v

# Run specific test file
python -m pytest pm_ai/tests/test_agentic_framework.py -v

# Run with coverage
python -m pytest pm_ai/tests/ --cov=pm_ai --cov-report=html
```

---

## Final Integration and Validation

### Complete System Test

After implementing all tasks, perform this comprehensive system test:

```bash
# 1. Install dependencies
poetry install

# 2. Set environment variables
export OPENAI_API_KEY="your-api-key-here"
export PM_AI_DEBUG="true"

# 3. Run the enhanced application
python -m pm_ai.main

# 4. Test natural language queries:
# - "How's my beachfront property doing lately?"
# - "Compare P123 to similar properties"
# - "Any recommendations for improving revenue?"
# - "What's unusual about the downtown properties?"
# - "Show me the luxury cohort performance"

# 5. Test conversation continuity:
# - Ask about a property, then ask "What about last quarter?"
# - Reference previous topics: "How does that compare to P456?"

# 6. Run test suite
python -m pytest pm_ai/tests/ -v
```

### Success Criteria for Phase 1

- [ ] **Natural Language Understanding**: System understands queries without exact patterns
- [ ] **Conversation Context**: Follow-up questions work with context from previous turns
- [ ] **Tool Integration**: All existing tools work with new framework
- [ ] **Error Handling**: Graceful handling of API failures and invalid inputs
- [ ] **Performance**: Response time under 5 seconds for typical queries
- [ ] **Backward Compatibility**: Existing functionality preserved
- [ ] **Test Coverage**: All critical paths covered by automated tests

### Common Issues and Solutions

**Issue**: OpenAI API rate limits
**Solution**: Implement exponential backoff in LLMEngine retry logic

**Issue**: Context memory growing too large
**Solution**: Implement context pruning in MemoryStore cleanup

**Issue**: Tool parameter extraction failures
**Solution**: Add validation and fallback parameter defaults

**Issue**: Session management memory leaks
**Solution**: Verify session cleanup in MemoryStore works correctly

---

## Next Steps (Phase 2 Preview)

After Phase 1 completion, Phase 2 will focus on:
- Advanced multi-step reasoning chains
- Proactive insight generation
- Enhanced context-aware clarification
- Performance optimization and caching
- Advanced error recovery mechanisms

This completes the Phase 1 implementation guide. All tasks are designed to be executed independently by engineers with the provided context and specifications.
