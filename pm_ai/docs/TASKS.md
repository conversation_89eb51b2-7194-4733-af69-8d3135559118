# Phase 1: LLM Integration Foundation - Implementation Tasks (Hybrid Testing Strategy)

## Project Overview

**Objective**: Transform the Property Manager AI from a rule-based pattern matching system to an agentic LLM-powered system using a hybrid testing strategy that prevents API key handling conflicts and ensures reliable validation.

**Current State**: The system uses hardcoded regex patterns in `pm_ai/agents/__init__.py` (lines 44-249) to match specific query formats and manually extract parameters.

**Target State**: Replace rule-based matching with OpenAI GPT-4 integration that can understand natural language queries, maintain conversation context, and intelligently select appropriate tools.

**Testing Strategy**: Three-tier architecture to address API key handling conflicts:
- **Unit Tests (Mocked)**: Fast tests with mock OpenAI clients, no API costs
- **Integration Tests (Real API)**: Limited real API calls for critical validation (max 50 calls per run)
- **End-to-End Tests (Real API)**: Complete system validation with full conversation flows

**Timeline**: 2 weeks (Phase 1 of 4-phase project)

---

## Environment Setup

### Prerequisites
- Python 3.13+
- OpenAI API key with GPT-4 access
- Existing Property Manager AI codebase
- Poetry for dependency management

### Environment Configuration (Test-Aware)
Create separate environment files to prevent API key conflicts:

**Production Environment (.env):**
```bash
# Production/development API usage
OPENAI_API_KEY=your_production_openai_api_key_here
OPENAI_MODEL=gpt-4o-mini  # or gpt-4
OPENAI_MAX_TOKENS=4096
OPENAI_TEMPERATURE=0.1
```

**Test Environment (.env.test):**
```bash
# Test-specific configuration
PM_AI_TEST_MODE=unit  # Options: unit, integration, e2e
OPENAI_API_KEY_TEST=your_test_openai_api_key_here  # Optional separate test key
PM_AI_DEBUG=true
PM_AI_MAX_API_CALLS_PER_TEST_RUN=50  # Cost control for integration tests
```

### Dependency Updates
Update `pyproject.toml` to include testing dependencies:
```toml
dependencies = [
    "openai (>=1.88.0,<2.0.0)",
    "openai-agents (>=0.0.19,<0.0.20)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "requests (>=2.31.0,<3.0.0)",
    "pydantic (>=2.0.0,<3.0.0)",
    "tiktoken (>=0.5.0,<1.0.0)",
    "pytest (>=7.0.0)",
    "pytest-mock (>=3.10.0)",
    "pytest-cov (>=4.0.0)",
    "pytest-env (>=0.8.0)",
    "pytest-xdist (>=3.0.0)",
]

[tool.pytest.ini_options]
testpaths = ["pm_ai/tests"]
python_files = ["test_*.py"]
python_classes = ["Test*"]
python_functions = ["test_*"]
markers = [
    "unit: Unit tests with mocked dependencies",
    "integration: Integration tests with real API calls",
    "e2e: End-to-end tests with full system",
    "slow: Tests that take longer than 30 seconds"
]
env = [
    "PM_AI_TEST_MODE=unit"
]

[tool.coverage.run]
source = ["pm_ai"]
omit = ["pm_ai/tests/*", "pm_ai/__pycache__/*"]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "raise AssertionError",
    "raise NotImplementedError"
]
```

---

## File Structure Overview

The following new files will be created in Phase 1 with hybrid testing strategy:

```
pm_ai/
├── intelligence/              # NEW: Core LLM intelligence modules
│   ├── __init__.py
│   ├── llm_engine.py         # OpenAI GPT-4 integration (test-aware)
│   ├── intent_parser.py      # Natural language intent recognition
│   └── context_manager.py    # Conversation context management
├── conversation/             # NEW: Conversation handling
│   ├── __init__.py
│   ├── session_manager.py    # Session state management
│   └── memory_store.py       # In-memory conversation storage
├── integration/              # NEW: Tool orchestration
│   ├── __init__.py
│   ├── tool_orchestrator.py  # Dynamic tool selection
│   └── result_synthesizer.py # Response formatting
├── config/                   # NEW: Test configuration system
│   ├── __init__.py
│   └── test_config.py        # Test environment management
├── tests/                    # ENHANCED: Three-tier testing
│   ├── conftest.py           # Pytest configuration and fixtures
│   ├── unit/                 # Unit tests with mocked dependencies
│   │   ├── __init__.py
│   │   ├── test_llm_engine.py
│   │   ├── test_intent_parser.py
│   │   └── test_context_manager.py
│   ├── integration/          # Integration tests with real API
│   │   ├── __init__.py
│   │   ├── test_llm_integration.py
│   │   └── test_intent_integration.py
│   └── e2e/                  # End-to-end system tests
│       ├── __init__.py
│       └── test_full_conversation.py
├── agents/                   # MODIFIED: Enhanced agent framework
│   ├── __init__.py           # Replace with LLM-powered version
│   └── property_manager.py   # Update with new agent definition
└── tools/                    # ENHANCED: Tool function improvements
    ├── enhanced_property_metrics.py    # NEW: Enhanced metrics tool
    ├── enhanced_property_comparisons.py # NEW: Enhanced comparisons
    └── property_resolver.py            # NEW: Property ID resolution
```

### Test Environment Files
```
project_root/
├── .env                      # Production environment variables
├── .env.test                 # Test-specific environment variables
└── pytest.ini               # Pytest configuration (optional)
```

---

## Task 1: Create Test-Aware LLM Engine Infrastructure

**Objective**: Establish the foundation for OpenAI GPT-4 integration with test-aware design that supports both mocked unit tests and real API integration tests, preventing API key handling conflicts.

**Files to Create**:
- `pm_ai/intelligence/llm_engine.py` (test-aware LLM engine)
- `pm_ai/config/test_config.py` (test environment management)
- `pm_ai/tests/conftest.py` (pytest fixtures)

**Dependencies**: None (foundational task)

**Estimated Time**: 6 hours

### Implementation Details

#### Step 1: Create Test Configuration System

First, create the test configuration system to manage environment separation:

```python
# pm_ai/config/test_config.py
"""
Test configuration management to prevent API key handling conflicts.
"""
import os
from typing import Optional

class TestConfig:
    """Test-specific configuration management."""

    @staticmethod
    def get_test_mode() -> str:
        """
        Determine test mode from environment.

        Returns:
            'unit' - Use mocks, no API calls
            'integration' - Use real API with test key
            'e2e' - Use real API with full system
        """
        return os.getenv("PM_AI_TEST_MODE", "unit")

    @staticmethod
    def get_openai_key() -> Optional[str]:
        """Get OpenAI API key based on test mode."""
        test_mode = TestConfig.get_test_mode()

        if test_mode == "unit":
            return None  # No API key needed for mocked tests
        elif test_mode in ["integration", "e2e"]:
            # Use test-specific key or fall back to main key
            return os.getenv("OPENAI_API_KEY_TEST") or os.getenv("OPENAI_API_KEY")

        return os.getenv("OPENAI_API_KEY")

    @staticmethod
    def should_use_real_api() -> bool:
        """Determine if tests should use real API calls."""
        return TestConfig.get_test_mode() in ["integration", "e2e"]

    @staticmethod
    def get_max_api_calls() -> int:
        """Get maximum API calls allowed per test run."""
        return int(os.getenv("PM_AI_MAX_API_CALLS_PER_TEST_RUN", "50"))
```

#### Step 2: Create Test-Aware LLM Engine

```python
# pm_ai/intelligence/llm_engine.py
"""
Test-aware LLM engine for OpenAI GPT-4 integration.
Supports both mocked unit tests and real API integration tests.
"""
import os
import time
import json
import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from openai import OpenAI
import tiktoken

@dataclass
class LLMConfig:
    """Configuration for LLM engine."""
    model: str = "gpt-4o-mini"
    max_tokens: int = 4096
    temperature: float = 0.1
    max_retries: int = 3
    retry_delay: float = 1.0
    timeout: int = 30
    test_mode: bool = False  # NEW: Enable test-specific behavior

class LLMEngine:
    """
    Test-aware LLM engine for OpenAI GPT-4 integration.

    Prevents API key handling conflicts by supporting:
    - Mock clients for unit tests (no API calls)
    - Real API clients for integration tests (limited calls)
    - Clear environment separation
    """

    def __init__(self, config: Optional[LLMConfig] = None, mock_client=None):
        self.config = config or LLMConfig()

        # Test-aware client initialization
        if mock_client:
            # Use provided mock client (for unit tests)
            self.client = mock_client
            self.is_mocked = True
        elif self.config.test_mode:
            # Test mode with real API but special handling
            api_key = self._get_test_api_key()
            self.client = OpenAI(api_key=api_key) if api_key else None
            self.is_mocked = False
        else:
            # Production mode
            api_key = os.getenv("OPENAI_API_KEY")
            if not api_key:
                raise ValueError("OPENAI_API_KEY environment variable is required")
            self.client = OpenAI(api_key=api_key)
            self.is_mocked = False

        # Initialize encoding if we have a real client
        if self.client and not self.is_mocked:
            try:
                self.encoding = tiktoken.encoding_for_model(self.config.model)
            except Exception:
                # Fallback for test environments
                self.encoding = tiktoken.get_encoding("cl100k_base")
        else:
            self.encoding = None

        self.logger = logging.getLogger(__name__)
        self.api_call_count = 0  # Track API calls for cost control

    def _get_test_api_key(self) -> Optional[str]:
        """Get API key for testing."""
        try:
            from ..config.test_config import TestConfig
            return TestConfig.get_openai_key()
        except ImportError:
            # Fallback if test_config not available
            return os.getenv("OPENAI_API_KEY_TEST") or os.getenv("OPENAI_API_KEY")

    def count_tokens(self, text: str) -> int:
        """Count tokens in text using tiktoken."""
        if self.encoding:
            return len(self.encoding.encode(text))
        else:
            # Fallback estimation for mocked tests
            return len(text.split()) * 1.3  # Rough approximation

    def create_completion(
        self,
        messages: List[Dict[str, str]],
        functions: Optional[List[Dict]] = None,
        function_call: Optional[Union[str, Dict]] = None
    ) -> Dict[str, Any]:
        """
        Create completion with test-aware behavior.

        Args:
            messages: List of conversation messages
            functions: Available functions for function calling
            function_call: Function call specification

        Returns:
            Dict containing response and metadata
        """

        if not self.client:
            raise ValueError("No OpenAI client available (missing API key)")

        # For mocked clients, return test response
        if self.is_mocked:
            return self._create_mock_response(messages, functions, function_call)

        # Track API calls for cost control
        self.api_call_count += 1

        # Real API call logic
        for attempt in range(self.config.max_retries):
            try:
                request_params = {
                    "model": self.config.model,
                    "messages": messages,
                    "max_tokens": self.config.max_tokens,
                    "temperature": self.config.temperature,
                    "timeout": self.config.timeout
                }

                if functions:
                    request_params["tools"] = [{"type": "function", "function": f} for f in functions]
                if function_call:
                    if isinstance(function_call, str):
                        request_params["tool_choice"] = {"type": "function", "function": {"name": function_call}}
                    else:
                        request_params["tool_choice"] = function_call

                response = self.client.chat.completions.create(**request_params)
                return self._process_response(response, messages)

            except Exception as e:
                self.logger.warning(f"LLM API attempt {attempt + 1} failed: {str(e)}")
                if attempt == self.config.max_retries - 1:
                    raise
                time.sleep(self.config.retry_delay * (2 ** attempt))

    def _create_mock_response(self, messages, functions, function_call) -> Dict[str, Any]:
        """Create mock response for testing."""
        # Default mock response
        mock_response = {
            "content": "Mock response for testing",
            "function_call": None,
            "finish_reason": "stop",
            "usage": {"prompt_tokens": 10, "completion_tokens": 5, "total_tokens": 15}
        }

        # If function calling is expected, create mock function call
        if functions and function_call:
            if isinstance(function_call, dict) and "name" in function_call:
                function_name = function_call["name"]
            elif isinstance(function_call, str):
                function_name = function_call
            else:
                function_name = functions[0]["name"] if functions else "mock_function"

            mock_response["function_call"] = {
                "name": function_name,
                "arguments": {"mock": "response", "confidence": 0.95}
            }
            mock_response["content"] = None

        return mock_response

    def _process_response(self, response, messages: List[Dict]) -> Dict[str, Any]:
        """Process OpenAI API response and extract relevant information."""
        choice = response.choices[0]
        message = choice.message

        result = {
            "content": message.content,
            "function_call": None,
            "finish_reason": choice.finish_reason,
            "usage": {
                "prompt_tokens": response.usage.prompt_tokens,
                "completion_tokens": response.usage.completion_tokens,
                "total_tokens": response.usage.total_tokens
            }
        }

        # Handle tool calls (new OpenAI API format)
        if hasattr(message, 'tool_calls') and message.tool_calls:
            tool_call = message.tool_calls[0]
            if tool_call.type == "function":
                result["function_call"] = {
                    "name": tool_call.function.name,
                    "arguments": json.loads(tool_call.function.arguments)
                }

        return result

    def get_api_call_count(self) -> int:
        """Get number of API calls made by this engine instance."""
        return self.api_call_count
```

#### Step 3: Create Pytest Fixtures

```python
# pm_ai/tests/conftest.py
"""
Pytest configuration and fixtures for hybrid testing strategy.
"""
import pytest
import os
from unittest.mock import Mock, MagicMock
from typing import Dict, Any

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig
from pm_ai.config.test_config import TestConfig

@pytest.fixture(scope="session")
def test_mode():
    """Determine test mode for the session."""
    return TestConfig.get_test_mode()

@pytest.fixture
def mock_openai_client():
    """Create a mock OpenAI client for unit tests."""
    mock_client = Mock()

    # Mock response structure
    mock_response = Mock()
    mock_response.choices = [Mock()]
    mock_response.choices[0].message.content = "Mock response content"
    mock_response.choices[0].message.tool_calls = None
    mock_response.choices[0].finish_reason = "stop"
    mock_response.usage.prompt_tokens = 10
    mock_response.usage.completion_tokens = 5
    mock_response.usage.total_tokens = 15

    mock_client.chat.completions.create.return_value = mock_response
    return mock_client

@pytest.fixture
def mock_llm_engine(mock_openai_client):
    """Create LLM engine with mocked client for unit tests."""
    config = LLMConfig(test_mode=True)
    return LLMEngine(config=config, mock_client=mock_openai_client)

@pytest.fixture
def real_llm_engine():
    """Create LLM engine with real API for integration tests."""
    if not TestConfig.should_use_real_api():
        pytest.skip("Real API tests disabled (set PM_AI_TEST_MODE=integration)")

    api_key = TestConfig.get_openai_key()
    if not api_key:
        pytest.skip("No OpenAI API key available for integration tests")

    config = LLMConfig(test_mode=True)
    return LLMEngine(config=config)

@pytest.fixture
def api_call_tracker():
    """Track API calls to enforce cost limits."""
    class APICallTracker:
        def __init__(self):
            self.call_count = 0
            self.max_calls = TestConfig.get_max_api_calls()

        def increment(self):
            self.call_count += 1
            if self.call_count > self.max_calls:
                pytest.fail(f"Exceeded maximum API calls ({self.max_calls}) for test run")

    return APICallTracker()
```

#### Step 4: Create Unit Tests (Mocked)

```python
# pm_ai/tests/unit/test_llm_engine.py
"""
Unit tests for LLM engine with mocked dependencies.
"""
import pytest
from unittest.mock import Mock, patch
import os

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig

@pytest.mark.unit
class TestLLMEngineUnit:
    """Unit tests for LLM engine with mocked dependencies."""

    def test_initialization_with_mock(self, mock_llm_engine):
        """Test LLM engine initializes correctly with mock client."""
        assert mock_llm_engine.client is not None
        assert mock_llm_engine.is_mocked is True
        assert mock_llm_engine.config.test_mode is True

    def test_create_completion_mocked(self, mock_llm_engine):
        """Test completion creation with mocked client."""
        messages = [{"role": "user", "content": "Test message"}]

        result = mock_llm_engine.create_completion(messages)

        assert result["content"] == "Mock response for testing"
        assert result["usage"]["total_tokens"] == 15
        assert mock_llm_engine.client.chat.completions.create.called

    def test_function_calling_mock(self, mock_llm_engine):
        """Test function calling with mocked response."""
        messages = [{"role": "user", "content": "How is P123 performing?"}]
        functions = [{"name": "extract_intent", "description": "Extract intent"}]

        result = mock_llm_engine.create_completion(
            messages,
            functions,
            function_call="extract_intent"
        )

        # Verify function call was processed
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "extract_intent"

    def test_token_counting_fallback(self, mock_llm_engine):
        """Test token counting with fallback for mocked engine."""
        text = "Hello world test"
        count = mock_llm_engine.count_tokens(text)

        # Should use fallback estimation
        assert count > 0
        assert isinstance(count, (int, float))

    def test_api_call_tracking(self, mock_llm_engine):
        """Test API call counting for cost control."""
        initial_count = mock_llm_engine.get_api_call_count()

        messages = [{"role": "user", "content": "Test"}]
        mock_llm_engine.create_completion(messages)

        # Mocked calls should not increment counter
        assert mock_llm_engine.get_api_call_count() == initial_count
```

#### Step 5: Create Integration Tests (Real API)

```python
# pm_ai/tests/integration/test_llm_integration.py
"""
Integration tests using real OpenAI API with cost controls.
"""
import pytest
import os

from pm_ai.intelligence.llm_engine import LLMEngine, LLMConfig

@pytest.mark.integration
class TestLLMIntegration:
    """Integration tests using real OpenAI API."""

    def test_real_api_completion(self, real_llm_engine, api_call_tracker):
        """Test actual API completion with cost tracking."""
        api_call_tracker.increment()

        messages = [
            {"role": "system", "content": "You are a helpful assistant."},
            {"role": "user", "content": "Say 'Hello, World!' and nothing else."}
        ]

        result = real_llm_engine.create_completion(messages)

        assert result["content"] is not None
        assert "hello" in result["content"].lower()
        assert result["usage"]["total_tokens"] > 0
        assert real_llm_engine.get_api_call_count() == 1

    def test_function_calling_real_api(self, real_llm_engine, api_call_tracker):
        """Test function calling with real API."""
        api_call_tracker.increment()

        messages = [{"role": "user", "content": "Extract intent from: How is P123 performing?"}]
        functions = [{
            "name": "extract_intent",
            "description": "Extract intent from property query",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {"type": "string"},
                    "property_id": {"type": "string"}
                }
            }
        }]

        result = real_llm_engine.create_completion(
            messages,
            functions,
            function_call="extract_intent"
        )

        # Should get a function call response
        assert result["function_call"] is not None
        assert result["function_call"]["name"] == "extract_intent"

    @pytest.mark.slow
    def test_retry_logic_real_api(self, real_llm_engine):
        """Test retry logic with real API (marked as slow)."""
        # This test may be slow due to retry delays
        # Only run in comprehensive test suites
        pass
```

### Acceptance Criteria

#### Unit Test Acceptance Criteria
- [ ] LLMEngine initializes correctly with mock client
- [ ] Mock responses are generated without API calls
- [ ] Function calling works with mocked responses
- [ ] Token counting fallback works for mocked tests
- [ ] All unit tests execute in under 30 seconds
- [ ] No real API calls are made during unit tests

#### Integration Test Acceptance Criteria
- [ ] LLMEngine works with real OpenAI API
- [ ] API call tracking prevents cost overruns
- [ ] Function calling works with real API responses
- [ ] Integration tests respect API call limits (max 50 per run)
- [ ] Real API tests pass consistently (>95% success rate)
- [ ] Environment separation prevents test/production conflicts

### Testing Commands

```bash
# Unit tests only (fast, no API calls)
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/test_llm_engine.py -v

# Integration tests (real API calls, requires key)
PM_AI_TEST_MODE=integration pytest pm_ai/tests/integration/test_llm_integration.py -v

# All tests with coverage
PM_AI_TEST_MODE=unit pytest pm_ai/tests/ --cov=pm_ai.intelligence --cov-report=html

# Test with API call limit enforcement
PM_AI_TEST_MODE=integration PM_AI_MAX_API_CALLS_PER_TEST_RUN=10 pytest pm_ai/tests/integration/ -v
```

### Error Recovery
- **Missing API Key**: Check .env and .env.test files, ensure OPENAI_API_KEY is set
- **API Key Conflicts**: Verify PM_AI_TEST_MODE is set correctly for test type
- **Import Errors**: Run `poetry install` to install testing dependencies
- **API Failures**: Check OpenAI service status and API key validity
- **Cost Overruns**: Reduce PM_AI_MAX_API_CALLS_PER_TEST_RUN or use unit tests only
- **Mock Failures**: Verify pytest fixtures are properly configured in conftest.py

---

## Task 2: Implement Test-Aware Intent Recognition System

**Objective**: Create a natural language intent parser with test-aware design that supports both mocked unit tests and real API integration tests for intent recognition validation.

**Files to Create**:
- `pm_ai/intelligence/intent_parser.py` (test-aware intent parser)
- `pm_ai/tests/unit/test_intent_parser.py` (unit tests with mocked LLM)
- `pm_ai/tests/integration/test_intent_integration.py` (integration tests with real API)

**Dependencies**: Task 1 (Test-Aware LLM Engine)

**Estimated Time**: 8 hours

### Implementation Details

#### Step 1: Create Test-Aware Intent Parser

```python
# pm_ai/intelligence/intent_parser.py
"""
Test-aware natural language intent recognition for property management queries.
Supports both mocked unit tests and real API integration tests.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
from enum import Enum
import json
import logging

from .llm_engine import LLMEngine

class QueryIntent(Enum):
    """Possible user intents for property management queries."""
    PROPERTY_PERFORMANCE = "property_performance"
    PROPERTY_COMPARISON = "property_comparison"
    RECOMMENDATIONS = "recommendations"
    OUTLIER_ANALYSIS = "outlier_analysis"
    COHORT_ANALYSIS = "cohort_analysis"
    GENERAL_INQUIRY = "general_inquiry"
    CLARIFICATION_NEEDED = "clarification_needed"

@dataclass
class ParsedIntent:
    """Structured representation of parsed user intent."""
    intent: QueryIntent
    confidence: float
    parameters: Dict[str, Any]
    clarification_needed: Optional[str] = None
    suggested_actions: List[str] = None

class IntentParser:
    """
    Test-aware natural language intent parser for property management queries.

    Supports both mocked unit tests and real API integration tests:
    - Unit tests use mocked LLM responses for fast validation
    - Integration tests use real API calls for accuracy validation
    """

    def __init__(self, llm_engine: LLMEngine):
        self.llm_engine = llm_engine
        self.system_prompt = self._create_system_prompt()
        self.logger = logging.getLogger(__name__)
        self.parse_count = 0  # Track parsing operations for testing

    def parse_query(self, query: str, context: Optional[Dict] = None) -> ParsedIntent:
        """
        Parse user query to extract intent and parameters.

        Args:
            query: User's natural language query
            context: Optional conversation context

        Returns:
            ParsedIntent with structured information
        """
        self.parse_count += 1

        # Handle empty or invalid queries
        if not query or not query.strip():
            return ParsedIntent(
                intent=QueryIntent.CLARIFICATION_NEEDED,
                confidence=0.0,
                parameters={},
                clarification_needed="Please provide a question about your properties."
            )

        messages = [
            {"role": "system", "content": self.system_prompt},
            {"role": "user", "content": self._format_query(query, context)}
        ]

        functions = [self._get_intent_extraction_function()]

        try:
            response = self.llm_engine.create_completion(
                messages=messages,
                functions=functions,
                function_call="extract_intent"
            )

            if response["function_call"]:
                return self._process_intent_response(response["function_call"]["arguments"])
            else:
                # Fallback if function calling fails
                return self._create_fallback_intent(query)

        except Exception as e:
            self.logger.error(f"Intent parsing failed for query '{query}': {str(e)}")
            return self._create_error_intent(str(e))

    def _create_fallback_intent(self, query: str) -> ParsedIntent:
        """Create fallback intent when LLM parsing fails."""
        return ParsedIntent(
            intent=QueryIntent.GENERAL_INQUIRY,
            confidence=0.3,
            parameters={"original_query": query},
            clarification_needed="I couldn't understand your request. Could you please rephrase?"
        )

    def _create_error_intent(self, error_message: str) -> ParsedIntent:
        """Create error intent when parsing completely fails."""
        return ParsedIntent(
            intent=QueryIntent.CLARIFICATION_NEEDED,
            confidence=0.0,
            parameters={"error": error_message},
            clarification_needed="I'm having trouble processing your request. Please try again."
        )

    def _create_system_prompt(self) -> str:
        """Create system prompt for intent recognition."""
        return """You are an expert at understanding property management queries.

Your task is to analyze user queries about vacation rental properties and extract:
1. The user's intent (what they want to accomplish)
2. Relevant parameters (property IDs, time periods, metrics, etc.)
3. Confidence level in your interpretation
4. Whether clarification is needed

Property Context:
- Properties are identified by IDs like P123, P456, P789
- Cohorts include: beachfront, downtown, mountain_view, luxury
- Time periods can be: last_30_days, last_quarter, last_year, or custom ranges
- Common metrics: occupancy_rate, average_daily_rate, total_revenue

Handle natural language variations like:
- "How's my beachfront place doing?" → property_performance for beachfront cohort
- "Compare P123 to similar properties" → property_comparison for P123
- "Any recommendations for the downtown rental?" → recommendations for downtown cohort
- "What's unusual about P456?" → outlier_analysis for P456

Always extract structured parameters and indicate confidence level."""

    def _format_query(self, query: str, context: Optional[Dict]) -> str:
        """Format query with context for LLM processing."""
        formatted = f"User Query: {query}"

        if context:
            formatted += f"\n\nContext: {json.dumps(context, indent=2)}"

        return formatted

    def _get_intent_extraction_function(self) -> Dict:
        """Define function schema for intent extraction."""
        return {
            "name": "extract_intent",
            "description": "Extract intent and parameters from property management query",
            "parameters": {
                "type": "object",
                "properties": {
                    "intent": {
                        "type": "string",
                        "enum": [intent.value for intent in QueryIntent],
                        "description": "Primary intent of the user query"
                    },
                    "confidence": {
                        "type": "number",
                        "minimum": 0.0,
                        "maximum": 1.0,
                        "description": "Confidence level in intent recognition (0.0-1.0)"
                    },
                    "parameters": {
                        "type": "object",
                        "properties": {
                            "property_ids": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific property IDs mentioned (e.g., P123, P456)"
                            },
                            "cohort": {
                                "type": "string",
                                "enum": ["beachfront", "downtown", "mountain_view", "luxury"],
                                "description": "Property cohort/group mentioned"
                            },
                            "time_period": {
                                "type": "string",
                                "description": "Time period for analysis (e.g., last_30_days, this_month)"
                            },
                            "metrics": {
                                "type": "array",
                                "items": {"type": "string"},
                                "description": "Specific metrics mentioned (occupancy, revenue, etc.)"
                            },
                            "comparison_type": {
                                "type": "string",
                                "description": "Type of comparison requested (vs cohort, vs previous period)"
                            }
                        }
                    },
                    "clarification_needed": {
                        "type": "string",
                        "description": "Question to ask user if query is ambiguous"
                    },
                    "suggested_actions": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Suggested follow-up actions or tools to use"
                    }
                },
                "required": ["intent", "confidence", "parameters"]
            }
        }

    def _process_intent_response(self, arguments: Dict) -> ParsedIntent:
        """Process function call response into ParsedIntent object."""
        try:
            return ParsedIntent(
                intent=QueryIntent(arguments["intent"]),
                confidence=arguments["confidence"],
                parameters=arguments["parameters"],
                clarification_needed=arguments.get("clarification_needed"),
                suggested_actions=arguments.get("suggested_actions", [])
            )
        except (KeyError, ValueError) as e:
            self.logger.error(f"Failed to process intent response: {e}")
            return self._create_error_intent(f"Invalid response format: {e}")

    def get_parse_count(self) -> int:
        """Get number of parse operations performed (for testing)."""
        return self.parse_count
```

#### Step 2: Create Pytest Fixtures for Intent Parser

```python
# Add to pm_ai/tests/conftest.py
@pytest.fixture
def mock_intent_response():
    """Mock response for intent parsing function calls."""
    return {
        "intent": "property_performance",
        "confidence": 0.95,
        "parameters": {
            "property_ids": ["P123"],
            "time_period": "last_30_days",
            "metrics": ["occupancy_rate", "revenue"]
        },
        "clarification_needed": None,
        "suggested_actions": ["get_property_metrics"]
    }

@pytest.fixture
def mock_intent_parser(mock_llm_engine):
    """Create intent parser with mocked LLM engine."""
    from pm_ai.intelligence.intent_parser import IntentParser
    return IntentParser(mock_llm_engine)

@pytest.fixture
def real_intent_parser(real_llm_engine):
    """Create intent parser with real LLM engine for integration tests."""
    from pm_ai.intelligence.intent_parser import IntentParser
    return IntentParser(real_llm_engine)
```

#### Step 3: Create Unit Tests (Mocked)

```python
# pm_ai/tests/unit/test_intent_parser.py
"""
Unit tests for intent parser with mocked LLM responses.
"""
import pytest
from unittest.mock import Mock, patch
import json

from pm_ai.intelligence.intent_parser import IntentParser, QueryIntent, ParsedIntent

@pytest.mark.unit
class TestIntentParserUnit:
    """Unit tests for intent parser with mocked dependencies."""

    def test_initialization(self, mock_intent_parser):
        """Test intent parser initializes correctly."""
        assert mock_intent_parser.llm_engine is not None
        assert mock_intent_parser.system_prompt is not None
        assert mock_intent_parser.get_parse_count() == 0

    def test_parse_property_performance_query(self, mock_intent_parser, mock_intent_response):
        """Test parsing property performance query with mocked response."""
        # Configure mock to return function call
        mock_intent_parser.llm_engine.client.chat.completions.create.return_value.choices[0].message.content = None

        # Mock function call response
        function_call_mock = Mock()
        function_call_mock.name = "extract_intent"
        function_call_mock.arguments = json.dumps(mock_intent_response)
        mock_intent_parser.llm_engine.client.chat.completions.create.return_value.choices[0].message.tool_calls = [
            Mock(type="function", function=function_call_mock)
        ]

        # Override the mock response method to return our test data
        def mock_create_completion(*args, **kwargs):
            return {
                "content": None,
                "function_call": {
                    "name": "extract_intent",
                    "arguments": mock_intent_response
                },
                "finish_reason": "function_call",
                "usage": {"total_tokens": 50}
            }

        mock_intent_parser.llm_engine.create_completion = mock_create_completion

        result = mock_intent_parser.parse_query("How is property P123 performing this month?")

        assert result.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert result.confidence == 0.95
        assert "P123" in result.parameters["property_ids"]
        assert result.parameters["time_period"] == "last_30_days"
        assert mock_intent_parser.get_parse_count() == 1

    def test_parse_empty_query(self, mock_intent_parser):
        """Test handling of empty queries."""
        result = mock_intent_parser.parse_query("")

        assert result.intent == QueryIntent.CLARIFICATION_NEEDED
        assert result.confidence == 0.0
        assert result.clarification_needed is not None

    def test_parse_error_handling(self, mock_intent_parser):
        """Test error handling when LLM fails."""
        # Configure mock to raise exception
        mock_intent_parser.llm_engine.create_completion = Mock(side_effect=Exception("API Error"))

        result = mock_intent_parser.parse_query("Test query")

        assert result.intent == QueryIntent.CLARIFICATION_NEEDED
        assert result.confidence == 0.0
        assert "error" in result.parameters

    def test_fallback_intent_creation(self, mock_intent_parser):
        """Test fallback intent when function calling fails."""
        # Configure mock to return no function call
        def mock_create_completion(*args, **kwargs):
            return {
                "content": "I couldn't parse that",
                "function_call": None,
                "finish_reason": "stop",
                "usage": {"total_tokens": 20}
            }

        mock_intent_parser.llm_engine.create_completion = mock_create_completion

        result = mock_intent_parser.parse_query("Unclear query")

        assert result.intent == QueryIntent.GENERAL_INQUIRY
        assert result.confidence == 0.3
        assert result.clarification_needed is not None

    def test_various_intent_types(self, mock_intent_parser):
        """Test parsing different types of intents."""
        test_cases = [
            ("Compare P123 to similar properties", QueryIntent.PROPERTY_COMPARISON),
            ("What recommendations do you have for P456?", QueryIntent.RECOMMENDATIONS),
            ("Show me outliers for P789", QueryIntent.OUTLIER_ANALYSIS),
            ("List downtown properties", QueryIntent.COHORT_ANALYSIS)
        ]

        for query, expected_intent in test_cases:
            # Mock response for each intent type
            def mock_create_completion(*args, **kwargs):
                return {
                    "content": None,
                    "function_call": {
                        "name": "extract_intent",
                        "arguments": {
                            "intent": expected_intent.value,
                            "confidence": 0.9,
                            "parameters": {"test": "data"}
                        }
                    },
                    "finish_reason": "function_call",
                    "usage": {"total_tokens": 30}
                }

            mock_intent_parser.llm_engine.create_completion = mock_create_completion
            result = mock_intent_parser.parse_query(query)

            assert result.intent == expected_intent
            assert result.confidence == 0.9
```

#### Step 4: Create Integration Tests (Real API)

```python
# pm_ai/tests/integration/test_intent_integration.py
"""
Integration tests for intent parser using real OpenAI API.
"""
import pytest

from pm_ai.intelligence.intent_parser import IntentParser, QueryIntent

@pytest.mark.integration
class TestIntentParserIntegration:
    """Integration tests using real OpenAI API with cost controls."""

    def test_real_property_performance_parsing(self, real_intent_parser, api_call_tracker):
        """Test property performance intent with real API."""
        api_call_tracker.increment()

        result = real_intent_parser.parse_query("How is property P123 performing this month?")

        assert result.intent == QueryIntent.PROPERTY_PERFORMANCE
        assert result.confidence > 0.7
        assert "property_ids" in result.parameters or "P123" in str(result.parameters)
        assert real_intent_parser.get_parse_count() == 1

    def test_real_natural_language_variations(self, real_intent_parser, api_call_tracker):
        """Test natural language variations with real API."""
        api_call_tracker.increment()

        # Test natural language query
        result = real_intent_parser.parse_query("How's my beachfront place doing lately?")

        assert result.intent in [QueryIntent.PROPERTY_PERFORMANCE, QueryIntent.COHORT_ANALYSIS]
        assert result.confidence > 0.5
        assert "beachfront" in str(result.parameters).lower() or "cohort" in result.parameters

    def test_real_comparison_intent(self, real_intent_parser, api_call_tracker):
        """Test comparison intent parsing with real API."""
        api_call_tracker.increment()

        result = real_intent_parser.parse_query("Compare P456 to similar properties")

        assert result.intent == QueryIntent.PROPERTY_COMPARISON
        assert result.confidence > 0.7
        assert "P456" in str(result.parameters) or "property_ids" in result.parameters

    def test_real_ambiguous_query_handling(self, real_intent_parser, api_call_tracker):
        """Test handling of ambiguous queries with real API."""
        api_call_tracker.increment()

        result = real_intent_parser.parse_query("Tell me about stuff")

        # Should either ask for clarification or have low confidence
        assert (result.intent == QueryIntent.CLARIFICATION_NEEDED or
                result.confidence < 0.7 or
                result.clarification_needed is not None)

    @pytest.mark.slow
    def test_real_context_awareness(self, real_intent_parser, api_call_tracker):
        """Test context-aware parsing with real API (marked as slow)."""
        api_call_tracker.increment()

        context = {
            "previous_property": "P123",
            "last_query_type": "property_performance"
        }

        result = real_intent_parser.parse_query("What about compared to last year?", context)

        # Should understand the context reference
        assert result.intent in [QueryIntent.PROPERTY_PERFORMANCE, QueryIntent.PROPERTY_COMPARISON]
        assert result.confidence > 0.5
```

### Acceptance Criteria

#### Unit Test Acceptance Criteria
- [ ] IntentParser initializes correctly with mocked LLM engine
- [ ] All QueryIntent types are correctly identified in unit tests
- [ ] Mocked responses generate appropriate ParsedIntent objects
- [ ] Error handling works with mocked failures
- [ ] Empty and invalid queries are handled gracefully
- [ ] Parse count tracking works for testing validation
- [ ] All unit tests execute in under 30 seconds

#### Integration Test Acceptance Criteria
- [ ] Real API correctly identifies property performance queries
- [ ] Natural language variations are parsed accurately (>80% accuracy)
- [ ] Property IDs and parameters are extracted correctly
- [ ] Confidence scoring reflects actual parsing quality
- [ ] Ambiguous queries trigger appropriate clarification requests
- [ ] Context-aware parsing works with conversation history
- [ ] Integration tests respect API call limits (max 10 calls for Task 2)

### Testing Commands

```bash
# Unit tests only (fast, no API calls)
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/test_intent_parser.py -v

# Integration tests (real API calls, limited)
PM_AI_TEST_MODE=integration pytest pm_ai/tests/integration/test_intent_integration.py -v

# Test specific intent types
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/test_intent_parser.py::TestIntentParserUnit::test_various_intent_types -v

# Integration tests with API call tracking
PM_AI_TEST_MODE=integration PM_AI_MAX_API_CALLS_PER_TEST_RUN=10 pytest pm_ai/tests/integration/test_intent_integration.py -v
```

### Error Recovery
- **Mock Configuration Issues**: Verify pytest fixtures are properly set up in conftest.py
- **Function Call Parsing Errors**: Check mock response format matches real API structure
- **Integration Test Failures**: Verify API key is valid and OpenAI service is available
- **Cost Overruns**: Reduce API call limits or focus on unit tests during development
- **Intent Recognition Accuracy**: Adjust system prompt or add more training examples
- **Context Handling Issues**: Verify context formatting and LLM engine integration

---

## Task 3: Create Test-Aware Conversation Context Management

**Objective**: Implement conversation memory and context management with test-aware design that supports both mocked unit tests and real conversation flow integration tests.

**Files to Create**:
- `pm_ai/conversation/memory_store.py` (test-aware memory storage)
- `pm_ai/conversation/session_manager.py` (session management with testing support)
- `pm_ai/intelligence/context_manager.py` (context resolution with mocked operations)
- `pm_ai/tests/unit/test_context_manager.py` (unit tests with mocked memory)
- `pm_ai/tests/integration/test_conversation_flow.py` (integration tests with real flows)

**Dependencies**: Task 1 (Test-Aware LLM Engine), Task 2 (Test-Aware Intent Parser)

**Estimated Time**: 10 hours

### Implementation Details

#### Step 1: Create Test-Aware Memory Store

```python
# pm_ai/conversation/memory_store.py
"""
Test-aware in-memory conversation storage for maintaining context across turns.
Supports both mocked unit tests and real conversation flow testing.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid
import logging

@dataclass
class ConversationTurn:
    """Single turn in a conversation."""
    turn_id: str
    timestamp: datetime
    user_query: str
    parsed_intent: Dict[str, Any]
    system_response: str
    tools_used: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConversationSession:
    """Complete conversation session with multiple turns."""
    session_id: str
    user_id: Optional[str]
    start_time: datetime
    last_activity: datetime
    turns: List[ConversationTurn] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)

class MemoryStore:
    """
    Test-aware in-memory storage for conversation sessions and context.

    Supports both unit testing with mocked data and integration testing
    with real conversation flows. In production, this would be replaced
    with persistent storage (Redis, PostgreSQL, etc.).
    """

    def __init__(self, test_mode: bool = False):
        self.sessions: Dict[str, ConversationSession] = {}
        self.max_sessions = 10 if test_mode else 100  # Smaller limits for testing
        self.max_turns_per_session = 10 if test_mode else 50
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)

        # Testing metrics
        self.operation_count = 0
        self.cleanup_count = 0

    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create new conversation session."""
        self.operation_count += 1

        session_id = str(uuid.uuid4())
        now = datetime.now()

        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            start_time=now,
            last_activity=now
        )

        self.sessions[session_id] = session
        self._cleanup_old_sessions()

        if self.test_mode:
            self.logger.debug(f"Created test session {session_id}")

        return session_id

    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """Retrieve conversation session."""
        self.operation_count += 1
        return self.sessions.get(session_id)

    def add_turn(
        self,
        session_id: str,
        user_query: str,
        parsed_intent: Dict[str, Any],
        system_response: str,
        tools_used: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Add new turn to conversation session."""
        self.operation_count += 1

        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")

        turn_id = str(uuid.uuid4())
        turn = ConversationTurn(
            turn_id=turn_id,
            timestamp=datetime.now(),
            user_query=user_query,
            parsed_intent=parsed_intent,
            system_response=system_response,
            tools_used=tools_used or [],
            metadata=metadata or {}
        )

        session.turns.append(turn)
        session.last_activity = datetime.now()

        # Limit turns per session
        if len(session.turns) > self.max_turns_per_session:
            session.turns = session.turns[-self.max_turns_per_session:]

        if self.test_mode:
            self.logger.debug(f"Added turn {turn_id} to session {session_id}")

        return turn_id

    def update_context(self, session_id: str, context_updates: Dict[str, Any]):
        """Update session context."""
        self.operation_count += 1

        session = self.sessions.get(session_id)
        if session:
            session.context.update(context_updates)
            session.last_activity = datetime.now()

            if self.test_mode:
                self.logger.debug(f"Updated context for session {session_id}")

    def get_recent_turns(self, session_id: str, count: int = 5) -> List[ConversationTurn]:
        """Get recent conversation turns."""
        self.operation_count += 1

        session = self.sessions.get(session_id)
        if not session:
            return []

        return session.turns[-count:] if session.turns else []

    def get_context(self, session_id: str) -> Dict[str, Any]:
        """Get session context for testing and context resolution."""
        session = self.sessions.get(session_id)
        if not session:
            return {}

        # Build comprehensive context from session data
        context = session.context.copy()

        # Add recent conversation context
        recent_turns = self.get_recent_turns(session_id, 3)
        if recent_turns:
            context["recent_queries"] = [turn.user_query for turn in recent_turns]
            context["recent_intents"] = [turn.parsed_intent for turn in recent_turns]
            context["last_tools_used"] = recent_turns[-1].tools_used if recent_turns else []

        return context

    def _cleanup_old_sessions(self):
        """Remove oldest sessions if limit exceeded."""
        if len(self.sessions) > self.max_sessions:
            self.cleanup_count += 1

            # Sort by last activity and remove oldest
            sorted_sessions = sorted(
                self.sessions.items(),
                key=lambda x: x[1].last_activity
            )

            sessions_to_remove = len(self.sessions) - self.max_sessions
            for session_id, _ in sorted_sessions[:sessions_to_remove]:
                del self.sessions[session_id]

                if self.test_mode:
                    self.logger.debug(f"Cleaned up old session {session_id}")

    def get_stats(self) -> Dict[str, Any]:
        """Get memory store statistics for testing."""
        return {
            "total_sessions": len(self.sessions),
            "operation_count": self.operation_count,
            "cleanup_count": self.cleanup_count,
            "max_sessions": self.max_sessions,
            "max_turns_per_session": self.max_turns_per_session
        }

    def clear_all_sessions(self):
        """Clear all sessions (for testing cleanup)."""
        if self.test_mode:
            self.sessions.clear()
            self.operation_count = 0
            self.cleanup_count = 0
            self.logger.debug("Cleared all test sessions")
```

#### Step 2: Create Session Manager

```python
# pm_ai/conversation/session_manager.py
"""
Test-aware session management for conversation handling.
"""
from typing import Dict, List, Optional, Any
import logging

from .memory_store import MemoryStore, ConversationSession

class SessionManager:
    """
    Test-aware session manager for conversation handling.

    Provides high-level interface for session operations with
    support for both unit testing and integration testing.
    """

    def __init__(self, memory_store: Optional[MemoryStore] = None, test_mode: bool = False):
        self.memory_store = memory_store or MemoryStore(test_mode=test_mode)
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)

    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create new conversation session."""
        return self.memory_store.create_session(user_id)

    def get_context(self, session_id: str) -> Dict[str, Any]:
        """Get conversation context for session."""
        return self.memory_store.get_context(session_id)

    def add_turn(
        self,
        session_id: str,
        user_query: str,
        parsed_intent: Dict[str, Any],
        system_response: str,
        tools_used: List[str] = None
    ) -> str:
        """Add conversation turn to session."""
        return self.memory_store.add_turn(
            session_id=session_id,
            user_query=user_query,
            parsed_intent=parsed_intent,
            system_response=system_response,
            tools_used=tools_used
        )

    def update_context(self, session_id: str, context_updates: Dict[str, Any]):
        """Update session context."""
        self.memory_store.update_context(session_id, context_updates)

    def get_session_info(self, session_id: str) -> Optional[Dict[str, Any]]:
        """Get session information for testing and debugging."""
        session = self.memory_store.get_session(session_id)
        if not session:
            return None

        return {
            "session_id": session.session_id,
            "user_id": session.user_id,
            "start_time": session.start_time,
            "last_activity": session.last_activity,
            "turn_count": len(session.turns),
            "context_keys": list(session.context.keys())
        }

    def get_stats(self) -> Dict[str, Any]:
        """Get session manager statistics for testing."""
        return self.memory_store.get_stats()
```

#### Step 3: Create Context Manager

```python
# pm_ai/intelligence/context_manager.py
"""
Test-aware context manager for conversation context resolution.
"""
from typing import Dict, List, Optional, Any
import logging

from ..conversation.session_manager import SessionManager

class ContextManager:
    """
    Test-aware context manager for conversation context resolution.

    Handles context extraction, reference resolution, and context
    building for both unit tests and integration tests.
    """

    def __init__(self, session_manager: SessionManager):
        self.session_manager = session_manager
        self.logger = logging.getLogger(__name__)

    def resolve_references(self, query: str, session_id: str) -> Dict[str, Any]:
        """
        Resolve pronoun and contextual references in query.

        Args:
            query: User query that may contain references
            session_id: Session ID for context lookup

        Returns:
            Dict with resolved references and context
        """
        context = self.session_manager.get_context(session_id)
        resolved_context = {}

        # Extract potential references
        references = self._extract_references(query)

        # Resolve each reference using session context
        for ref_type, ref_value in references.items():
            if ref_type == "property_reference":
                resolved_context["resolved_property"] = self._resolve_property_reference(
                    ref_value, context
                )
            elif ref_type == "time_reference":
                resolved_context["resolved_time"] = self._resolve_time_reference(
                    ref_value, context
                )
            elif ref_type == "comparison_reference":
                resolved_context["resolved_comparison"] = self._resolve_comparison_reference(
                    ref_value, context
                )

        # Add general context
        resolved_context["session_context"] = context
        resolved_context["reference_count"] = len(references)

        return resolved_context

    def _extract_references(self, query: str) -> Dict[str, str]:
        """Extract potential references from query."""
        references = {}
        query_lower = query.lower()

        # Property references
        if any(word in query_lower for word in ["it", "that property", "the property", "this one"]):
            references["property_reference"] = "pronoun"
        elif any(word in query_lower for word in ["my property", "the place", "that place"]):
            references["property_reference"] = "possessive"

        # Time references
        if any(word in query_lower for word in ["last time", "before", "previously", "earlier"]):
            references["time_reference"] = "previous"
        elif any(word in query_lower for word in ["compared to", "vs", "versus"]):
            references["comparison_reference"] = "comparative"

        return references

    def _resolve_property_reference(self, ref_type: str, context: Dict[str, Any]) -> Optional[str]:
        """Resolve property references using context."""
        if "recent_intents" in context:
            for intent in reversed(context["recent_intents"]):
                if "property_ids" in intent.get("parameters", {}):
                    property_ids = intent["parameters"]["property_ids"]
                    if property_ids:
                        return property_ids[0]  # Return most recent property

        return None

    def _resolve_time_reference(self, ref_type: str, context: Dict[str, Any]) -> Optional[str]:
        """Resolve time references using context."""
        if "recent_intents" in context:
            for intent in reversed(context["recent_intents"]):
                if "time_period" in intent.get("parameters", {}):
                    return intent["parameters"]["time_period"]

        return "last_30_days"  # Default fallback

    def _resolve_comparison_reference(self, ref_type: str, context: Dict[str, Any]) -> Optional[str]:
        """Resolve comparison references using context."""
        if "recent_intents" in context:
            for intent in reversed(context["recent_intents"]):
                if intent.get("intent") == "property_comparison":
                    return intent.get("parameters", {}).get("comparison_type", "cohort")

        return "similar_properties"  # Default fallback
```

#### Step 4: Create Unit Tests (Mocked)

```python
# pm_ai/tests/unit/test_context_manager.py
"""
Unit tests for context management with mocked memory operations.
"""
import pytest
from datetime import datetime
from unittest.mock import Mock, patch

from pm_ai.conversation.memory_store import MemoryStore, ConversationSession, ConversationTurn
from pm_ai.conversation.session_manager import SessionManager
from pm_ai.intelligence.context_manager import ContextManager

@pytest.mark.unit
class TestMemoryStoreUnit:
    """Unit tests for memory store with mocked operations."""

    def test_memory_store_initialization(self):
        """Test memory store initializes correctly in test mode."""
        store = MemoryStore(test_mode=True)

        assert store.test_mode is True
        assert store.max_sessions == 10  # Test mode limit
        assert store.max_turns_per_session == 10
        assert store.operation_count == 0

    def test_session_creation(self):
        """Test session creation and tracking."""
        store = MemoryStore(test_mode=True)

        session_id = store.create_session("test_user")

        assert session_id is not None
        assert store.operation_count == 1

        session = store.get_session(session_id)
        assert session is not None
        assert session.user_id == "test_user"
        assert len(session.turns) == 0

    def test_turn_addition(self):
        """Test adding conversation turns."""
        store = MemoryStore(test_mode=True)
        session_id = store.create_session()

        turn_id = store.add_turn(
            session_id=session_id,
            user_query="How is P123 performing?",
            parsed_intent={"intent": "property_performance", "property_id": "P123"},
            system_response="P123 is performing well",
            tools_used=["get_property_metrics"]
        )

        assert turn_id is not None
        session = store.get_session(session_id)
        assert len(session.turns) == 1
        assert session.turns[0].user_query == "How is P123 performing?"

    def test_context_updates(self):
        """Test context updates and retrieval."""
        store = MemoryStore(test_mode=True)
        session_id = store.create_session()

        context_updates = {
            "current_property": "P123",
            "user_preferences": {"detail_level": "high"}
        }

        store.update_context(session_id, context_updates)
        context = store.get_context(session_id)

        assert context["current_property"] == "P123"
        assert context["user_preferences"]["detail_level"] == "high"

    def test_session_cleanup(self):
        """Test session cleanup when limits are exceeded."""
        store = MemoryStore(test_mode=True)

        # Create more sessions than the limit
        session_ids = []
        for i in range(12):  # Exceeds test mode limit of 10
            session_id = store.create_session(f"user_{i}")
            session_ids.append(session_id)

        # Should have triggered cleanup
        assert len(store.sessions) <= store.max_sessions
        assert store.cleanup_count > 0

    def test_stats_tracking(self):
        """Test statistics tracking for testing validation."""
        store = MemoryStore(test_mode=True)

        # Perform various operations
        session_id = store.create_session()
        store.add_turn(session_id, "test", {}, "response")
        store.update_context(session_id, {"test": "value"})

        stats = store.get_stats()

        assert stats["total_sessions"] == 1
        assert stats["operation_count"] > 0
        assert "max_sessions" in stats

@pytest.mark.unit
class TestSessionManagerUnit:
    """Unit tests for session manager."""

    def test_session_manager_initialization(self):
        """Test session manager initializes with test mode."""
        manager = SessionManager(test_mode=True)

        assert manager.test_mode is True
        assert manager.memory_store is not None

    def test_session_operations(self):
        """Test high-level session operations."""
        manager = SessionManager(test_mode=True)

        # Create session
        session_id = manager.create_session("test_user")
        assert session_id is not None

        # Add turn
        turn_id = manager.add_turn(
            session_id=session_id,
            user_query="Test query",
            parsed_intent={"intent": "test"},
            system_response="Test response",
            tools_used=["test_tool"]
        )
        assert turn_id is not None

        # Get context
        context = manager.get_context(session_id)
        assert isinstance(context, dict)

        # Get session info
        info = manager.get_session_info(session_id)
        assert info["user_id"] == "test_user"
        assert info["turn_count"] == 1

@pytest.mark.unit
class TestContextManagerUnit:
    """Unit tests for context manager."""

    def test_context_manager_initialization(self):
        """Test context manager initializes correctly."""
        session_manager = SessionManager(test_mode=True)
        context_manager = ContextManager(session_manager)

        assert context_manager.session_manager is not None

    def test_reference_extraction(self):
        """Test extraction of references from queries."""
        session_manager = SessionManager(test_mode=True)
        context_manager = ContextManager(session_manager)

        # Test property references
        refs = context_manager._extract_references("How is that property doing?")
        assert "property_reference" in refs

        # Test time references
        refs = context_manager._extract_references("Compared to last time")
        assert "time_reference" in refs

    def test_reference_resolution(self):
        """Test resolution of references using context."""
        session_manager = SessionManager(test_mode=True)
        context_manager = ContextManager(session_manager)

        # Create session with context
        session_id = session_manager.create_session()
        session_manager.add_turn(
            session_id=session_id,
            user_query="How is P123 performing?",
            parsed_intent={
                "intent": "property_performance",
                "parameters": {"property_ids": ["P123"], "time_period": "last_30_days"}
            },
            system_response="P123 is doing well"
        )

        # Test reference resolution
        resolved = context_manager.resolve_references("How is it doing now?", session_id)

        assert "session_context" in resolved
        assert "reference_count" in resolved
```

#### Step 5: Create Integration Tests (Real Conversation Flows)

```python
# pm_ai/tests/integration/test_conversation_flow.py
"""
Integration tests for conversation flow with real context management.
"""
import pytest
from datetime import datetime

from pm_ai.conversation.memory_store import MemoryStore
from pm_ai.conversation.session_manager import SessionManager
from pm_ai.intelligence.context_manager import ContextManager

@pytest.mark.integration
class TestConversationFlowIntegration:
    """Integration tests for complete conversation flows."""

    def test_multi_turn_conversation_flow(self):
        """Test complete multi-turn conversation with context retention."""
        # Setup conversation components
        memory_store = MemoryStore(test_mode=False)  # Use production-like settings
        session_manager = SessionManager(memory_store)
        context_manager = ContextManager(session_manager)

        # Start conversation
        session_id = session_manager.create_session("integration_test_user")

        # Turn 1: Initial property query
        turn1_id = session_manager.add_turn(
            session_id=session_id,
            user_query="How is property P123 performing this month?",
            parsed_intent={
                "intent": "property_performance",
                "parameters": {"property_ids": ["P123"], "time_period": "last_30_days"}
            },
            system_response="Property P123 has 85% occupancy this month...",
            tools_used=["get_property_metrics"]
        )

        # Turn 2: Follow-up with reference
        context = session_manager.get_context(session_id)
        assert "recent_queries" in context
        assert "P123" in str(context)

        resolved_context = context_manager.resolve_references(
            "What about compared to last quarter?",
            session_id
        )

        turn2_id = session_manager.add_turn(
            session_id=session_id,
            user_query="What about compared to last quarter?",
            parsed_intent={
                "intent": "property_comparison",
                "parameters": {"property_ids": ["P123"], "time_period": "last_quarter"}
            },
            system_response="Compared to last quarter, P123 improved by 12%...",
            tools_used=["get_property_comparisons"]
        )

        # Turn 3: Another follow-up
        turn3_id = session_manager.add_turn(
            session_id=session_id,
            user_query="Any recommendations for improving it further?",
            parsed_intent={
                "intent": "recommendations",
                "parameters": {"property_ids": ["P123"]}
            },
            system_response="Consider updating amenities and adjusting pricing...",
            tools_used=["get_property_recommendations"]
        )

        # Verify conversation state
        session_info = session_manager.get_session_info(session_id)
        assert session_info["turn_count"] == 3
        assert session_info["user_id"] == "integration_test_user"

        # Verify context retention
        final_context = session_manager.get_context(session_id)
        assert len(final_context["recent_queries"]) == 3
        assert len(final_context["recent_intents"]) == 3

        # Verify reference resolution works across turns
        resolved = context_manager.resolve_references("How is it doing?", session_id)
        assert resolved["resolved_property"] == "P123"

    def test_session_persistence_and_cleanup(self):
        """Test session persistence and cleanup mechanisms."""
        memory_store = MemoryStore(test_mode=True)  # Use test limits
        session_manager = SessionManager(memory_store)

        # Create multiple sessions
        session_ids = []
        for i in range(15):  # Exceeds test limit of 10
            session_id = session_manager.create_session(f"user_{i}")
            session_ids.append(session_id)

            # Add some turns to make sessions active
            session_manager.add_turn(
                session_id=session_id,
                user_query=f"Query {i}",
                parsed_intent={"intent": "test", "parameters": {}},
                system_response=f"Response {i}"
            )

        # Verify cleanup occurred
        stats = session_manager.get_stats()
        assert stats["total_sessions"] <= memory_store.max_sessions
        assert stats["cleanup_count"] > 0

        # Verify most recent sessions are retained
        recent_sessions = session_ids[-5:]  # Last 5 sessions
        for session_id in recent_sessions:
            info = session_manager.get_session_info(session_id)
            if info:  # Some may have been cleaned up
                assert info["turn_count"] >= 1

    @pytest.mark.slow
    def test_large_conversation_handling(self):
        """Test handling of large conversations (marked as slow)."""
        memory_store = MemoryStore(test_mode=False)
        session_manager = SessionManager(memory_store)

        session_id = session_manager.create_session("large_conversation_user")

        # Add many turns to test turn limiting
        for i in range(60):  # Exceeds max_turns_per_session
            session_manager.add_turn(
                session_id=session_id,
                user_query=f"Query {i}",
                parsed_intent={"intent": "test", "turn": i},
                system_response=f"Response {i}"
            )

        # Verify turn limiting
        session_info = session_manager.get_session_info(session_id)
        assert session_info["turn_count"] <= memory_store.max_turns_per_session

        # Verify recent turns are retained
        context = session_manager.get_context(session_id)
        recent_queries = context.get("recent_queries", [])
        assert len(recent_queries) <= 3  # Should have recent queries
        assert "Query 59" in recent_queries[-1]  # Most recent should be preserved
```

### Acceptance Criteria

#### Unit Test Acceptance Criteria
- [ ] MemoryStore initializes correctly in test mode with appropriate limits
- [ ] Session creation, turn addition, and context updates work with mocked data
- [ ] Session cleanup mechanisms function properly with test limits
- [ ] Statistics tracking works for testing validation
- [ ] SessionManager provides high-level interface for session operations
- [ ] ContextManager extracts and resolves references correctly
- [ ] All unit tests execute in under 30 seconds

#### Integration Test Acceptance Criteria
- [ ] Multi-turn conversations maintain context across turns
- [ ] Reference resolution works with real conversation flows
- [ ] Session persistence and cleanup work with realistic data volumes
- [ ] Context building includes recent queries, intents, and tools used
- [ ] Large conversation handling respects memory limits
- [ ] Integration tests complete within 5 minutes

### Testing Commands

```bash
# Unit tests only (fast, no real conversation flows)
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/test_context_manager.py -v

# Integration tests (real conversation flows)
PM_AI_TEST_MODE=integration pytest pm_ai/tests/integration/test_conversation_flow.py -v

# Test specific components
pytest pm_ai/tests/unit/test_context_manager.py::TestMemoryStoreUnit::test_session_creation -v

# Test with coverage
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/test_context_manager.py --cov=pm_ai.conversation --cov-report=html
```

### Error Recovery
- **Memory Store Issues**: Verify test mode settings and session limits
- **Context Resolution Failures**: Check session data structure and recent turns
- **Session Cleanup Problems**: Verify cleanup logic and session limits
- **Integration Test Timeouts**: Reduce conversation size or mark tests as slow
- **Reference Resolution Errors**: Check context building and parameter extraction
- **Statistics Tracking Issues**: Verify operation counting and test mode flags

---

## Task 4: Implement Test-Aware Tool Orchestration System

**Objective**: Create intelligent tool selection and execution system with test-aware design that supports both mocked tool execution and real tool integration tests.

**Files to Create**:
- `pm_ai/integration/tool_orchestrator.py` (test-aware tool orchestration)
- `pm_ai/integration/result_synthesizer.py` (response synthesis with mocked results)
- `pm_ai/tests/unit/test_tool_orchestrator.py` (unit tests with mocked tools)
- `pm_ai/tests/integration/test_tool_integration.py` (integration tests with real tools)

**Dependencies**: Task 1 (Test-Aware LLM Engine), Task 2 (Test-Aware Intent Parser), Task 3 (Test-Aware Context Management)

**Estimated Time**: 8 hours

### Implementation Details

#### Step 1: Create Test-Aware Tool Orchestrator

```python
# pm_ai/integration/tool_orchestrator.py
"""
Test-aware dynamic tool selection and execution orchestration.
Supports both mocked tool execution and real tool integration tests.
"""
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
import logging
import time

from ..intelligence.intent_parser import ParsedIntent, QueryIntent
from ..intelligence.llm_engine import LLMEngine

@dataclass
class ToolResult:
    """Result from tool execution."""
    tool_name: str
    success: bool
    data: Any
    error_message: Optional[str] = None
    execution_time: float = 0.0
    is_mocked: bool = False

class ToolOrchestrator:
    """
    Test-aware intelligent tool selection and execution orchestration.

    Supports both mocked tool execution for unit tests and real tool
    execution for integration tests. Determines which tools to use
    based on parsed intent and executes them with proper error handling.
    """

    def __init__(self, llm_engine: LLMEngine, test_mode: bool = False):
        self.llm_engine = llm_engine
        self.available_tools: Dict[str, Callable] = {}
        self.tool_descriptions: Dict[str, str] = {}
        self.mock_tools: Dict[str, Callable] = {}
        self.test_mode = test_mode
        self.logger = logging.getLogger(__name__)

        # Testing metrics
        self.execution_count = 0
        self.tool_selection_count = 0

    def register_tool(self, name: str, function: Callable, description: str, mock_function: Optional[Callable] = None):
        """Register a tool function with the orchestrator."""
        self.available_tools[name] = function
        self.tool_descriptions[name] = description

        if mock_function:
            self.mock_tools[name] = mock_function
        elif self.test_mode:
            # Create default mock if none provided
            self.mock_tools[name] = self._create_default_mock(name)

    def _create_default_mock(self, tool_name: str) -> Callable:
        """Create default mock function for tool."""
        def mock_tool(**kwargs):
            return {
                "tool": tool_name,
                "parameters": kwargs,
                "mock_result": f"Mock result for {tool_name}",
                "success": True
            }
        return mock_tool

    def execute_intent(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> List[ToolResult]:
        """
        Execute tools based on parsed intent.

        Args:
            parsed_intent: Structured intent from intent parser
            context: Conversation context

        Returns:
            List of tool execution results
        """
        self.tool_selection_count += 1

        # Determine which tools to use
        selected_tools = self._select_tools(parsed_intent, context)

        # Execute tools in sequence
        results = []
        for tool_config in selected_tools:
            result = self._execute_tool(tool_config)
            results.append(result)

            # Stop execution if critical tool fails
            if not result.success and tool_config.get("critical", False):
                self.logger.error(f"Critical tool {result.tool_name} failed: {result.error_message}")
                break

        return results

    def _select_tools(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Select appropriate tools based on intent and context."""
        tools = []

        if parsed_intent.intent == QueryIntent.PROPERTY_PERFORMANCE:
            tools.append({
                "name": "get_property_metrics",
                "parameters": self._extract_metrics_parameters(parsed_intent, context),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.PROPERTY_COMPARISON:
            tools.append({
                "name": "get_property_comparisons",
                "parameters": self._extract_comparison_parameters(parsed_intent, context),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.RECOMMENDATIONS:
            tools.append({
                "name": "get_property_recommendations",
                "parameters": self._extract_recommendation_parameters(parsed_intent, context),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.OUTLIER_ANALYSIS:
            tools.append({
                "name": "get_outlier_statistics",
                "parameters": self._extract_outlier_parameters(parsed_intent, context),
                "critical": True
            })

        elif parsed_intent.intent == QueryIntent.COHORT_ANALYSIS:
            tools.append({
                "name": "get_cohort_properties",
                "parameters": self._extract_cohort_parameters(parsed_intent, context),
                "critical": True
            })

        return tools

    def _execute_tool(self, tool_config: Dict[str, Any]) -> ToolResult:
        """Execute a single tool with error handling and test awareness."""
        self.execution_count += 1

        tool_name = tool_config["name"]
        parameters = tool_config["parameters"]

        # Choose between real and mock tool execution
        if self.test_mode and tool_name in self.mock_tools:
            return self._execute_mock_tool(tool_name, parameters)
        elif tool_name in self.available_tools:
            return self._execute_real_tool(tool_name, parameters)
        else:
            return ToolResult(
                tool_name=tool_name,
                success=False,
                data=None,
                error_message=f"Tool {tool_name} not found",
                is_mocked=self.test_mode
            )

    def _execute_mock_tool(self, tool_name: str, parameters: Dict[str, Any]) -> ToolResult:
        """Execute mock tool for testing."""
        try:
            start_time = time.time()

            mock_function = self.mock_tools[tool_name]
            result = mock_function(**parameters)

            execution_time = time.time() - start_time

            return ToolResult(
                tool_name=tool_name,
                success=True,
                data=result,
                execution_time=execution_time,
                is_mocked=True
            )

        except Exception as e:
            self.logger.exception(f"Mock tool {tool_name} execution failed")
            return ToolResult(
                tool_name=tool_name,
                success=False,
                data=None,
                error_message=str(e),
                is_mocked=True
            )

    def _execute_real_tool(self, tool_name: str, parameters: Dict[str, Any]) -> ToolResult:
        """Execute real tool for production/integration testing."""
        try:
            start_time = time.time()

            tool_function = self.available_tools[tool_name]
            result = tool_function(**parameters)

            execution_time = time.time() - start_time

            # Check if result indicates an error
            if isinstance(result, dict) and "error" in result:
                return ToolResult(
                    tool_name=tool_name,
                    success=False,
                    data=result,
                    error_message=result["error"],
                    execution_time=execution_time,
                    is_mocked=False
                )

            return ToolResult(
                tool_name=tool_name,
                success=True,
                data=result,
                execution_time=execution_time,
                is_mocked=False
            )

        except Exception as e:
            self.logger.exception(f"Tool {tool_name} execution failed")
            return ToolResult(
                tool_name=tool_name,
                success=False,
                data=None,
                error_message=str(e),
                is_mocked=False
            )

    def _extract_metrics_parameters(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract parameters for property metrics tool with context awareness."""
        params = {}

        # Extract property ID from intent or context
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        elif "resolved_property" in context:
            params["property_id"] = context["resolved_property"]

        # Extract time period
        if "time_period" in parsed_intent.parameters:
            params["period"] = self._normalize_time_period(parsed_intent.parameters["time_period"])
        elif "resolved_time" in context:
            params["period"] = context["resolved_time"]
        else:
            params["period"] = "last_30_days"  # Default

        return params

    def _extract_comparison_parameters(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract parameters for property comparison tool with context awareness."""
        params = {}

        # Extract property ID from intent or context
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        elif "resolved_property" in context:
            params["property_id"] = context["resolved_property"]

        # Extract specific metric if mentioned
        if "metrics" in parsed_intent.parameters and parsed_intent.parameters["metrics"]:
            metric_mapping = {
                "occupancy": "occupancy_rate",
                "rate": "average_daily_rate",
                "revenue": "total_revenue"
            }
            for metric in parsed_intent.parameters["metrics"]:
                if metric.lower() in metric_mapping:
                    params["metric"] = metric_mapping[metric.lower()]
                    break

        return params

    def _extract_recommendation_parameters(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract parameters for recommendations tool with context awareness."""
        params = {}

        # Extract property ID from intent or context
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        elif "resolved_property" in context:
            params["property_id"] = context["resolved_property"]

        return params

    def _extract_outlier_parameters(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract parameters for outlier analysis tool with context awareness."""
        params = {}

        # Extract property ID from intent or context
        if "property_ids" in parsed_intent.parameters and parsed_intent.parameters["property_ids"]:
            params["property_id"] = parsed_intent.parameters["property_ids"][0]
        elif "resolved_property" in context:
            params["property_id"] = context["resolved_property"]

        return params

    def _extract_cohort_parameters(self, parsed_intent: ParsedIntent, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract parameters for cohort analysis tool with context awareness."""
        params = {}

        # Extract cohort name
        if "cohort" in parsed_intent.parameters:
            params["cohort_id"] = parsed_intent.parameters["cohort"]

        return params

    def _normalize_time_period(self, time_period: str) -> str:
        """Normalize natural language time periods to standard formats."""
        period_mapping = {
            "this month": "last_30_days",
            "last month": "last_30_days",
            "month": "last_30_days",
            "quarter": "last_quarter",
            "this quarter": "last_quarter",
            "year": "last_year",
            "this year": "last_year",
            "lately": "last_30_days",
            "recently": "last_30_days"
        }

        return period_mapping.get(time_period.lower(), "last_30_days")

    def get_stats(self) -> Dict[str, Any]:
        """Get orchestrator statistics for testing."""
        return {
            "execution_count": self.execution_count,
            "tool_selection_count": self.tool_selection_count,
            "available_tools": list(self.available_tools.keys()),
            "mock_tools": list(self.mock_tools.keys()),
            "test_mode": self.test_mode
        }
```

#### Step 2: Create Unit and Integration Tests for Tool Orchestrator

```python
# pm_ai/tests/unit/test_tool_orchestrator.py (Key sections)
@pytest.mark.unit
class TestToolOrchestratorUnit:
    def test_mock_tool_execution(self):
        """Test tool execution with mocked tools."""
        orchestrator = ToolOrchestrator(mock_llm_engine, test_mode=True)

        # Register mock tool
        def mock_get_property_metrics(**kwargs):
            return {"property_id": kwargs.get("property_id"), "occupancy": 85, "revenue": 5000}

        orchestrator.register_tool("get_property_metrics", None, "Mock metrics", mock_get_property_metrics)

        # Test execution
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"], "time_period": "last_30_days"}
        )

        results = orchestrator.execute_intent(parsed_intent, {})

        assert len(results) == 1
        assert results[0].success is True
        assert results[0].is_mocked is True
        assert "P123" in str(results[0].data)

# pm_ai/tests/integration/test_tool_integration.py (Key sections)
@pytest.mark.integration
class TestToolIntegration:
    def test_real_tool_execution(self, api_call_tracker):
        """Test tool execution with real tools (no API calls for tools themselves)."""
        # This tests tool orchestration without LLM API calls
        orchestrator = ToolOrchestrator(real_llm_engine, test_mode=False)

        # Register real tools from existing codebase
        from pm_ai.tools.property_metrics import get_property_metrics
        orchestrator.register_tool("get_property_metrics", get_property_metrics, "Real metrics tool")

        # Test with real tool execution
        parsed_intent = ParsedIntent(
            intent=QueryIntent.PROPERTY_PERFORMANCE,
            confidence=0.9,
            parameters={"property_ids": ["P123"], "time_period": "last_30_days"}
        )

        results = orchestrator.execute_intent(parsed_intent, {})

        assert len(results) == 1
        assert results[0].success is True
        assert results[0].is_mocked is False
        assert results[0].execution_time > 0
```

### Acceptance Criteria

#### Unit Test Acceptance Criteria
- [ ] Tool orchestrator correctly maps intents to tools with mocked execution
- [ ] Parameter extraction works for all tool types with context awareness
- [ ] Mock tool execution provides realistic test data
- [ ] Error handling prevents system crashes with mocked failures
- [ ] Tool registration system works with both real and mock tools
- [ ] Time period normalization handles natural language variations
- [ ] Critical tool failure stops execution appropriately
- [ ] All unit tests execute in under 30 seconds

#### Integration Test Acceptance Criteria
- [ ] Real tool execution works with existing property management tools
- [ ] Context-aware parameter extraction resolves references correctly
- [ ] Tool orchestration handles real tool failures gracefully
- [ ] Performance metrics are tracked for real tool execution
- [ ] Integration tests complete without API calls to LLM services
- [ ] Tool selection accuracy is validated with real scenarios

---

## Task 5: Enhanced Agent Framework (Summary)

**Objective**: Replace hardcoded rule-based agent with LLM-powered agentic framework integrating all components.

**Key Testing Approach**:
- **Unit Tests**: Mock all components (LLM, tools, context) for fast validation
- **Integration Tests**: Real component integration with limited API calls (max 15 calls)
- **E2E Tests**: Complete conversation flows with real API validation

**Files**: `pm_ai/agents/__init__.py` (complete replacement), test files for agent integration

---

## Task 6: Main Application Update (Summary)

**Objective**: Update main CLI application to use new agentic framework while maintaining interface.

**Key Testing Approach**:
- **Unit Tests**: Mock agent responses for CLI interface testing
- **Integration Tests**: Real agent integration with conversation flow testing
- **Manual Testing**: CLI interaction testing with real API calls

**Files**: `pm_ai/main.py` (modifications), CLI testing scripts

---

## Task 7: Comprehensive Test Suite Consolidation

**Objective**: Consolidate all testing strategies and create comprehensive test execution framework.

**Key Components**:
- **Test Configuration**: Centralized test mode management
- **API Call Tracking**: Cost control across all test types
- **Coverage Reporting**: Comprehensive coverage across all components
- **CI/CD Integration**: Automated test execution with proper environment separation

### Final Testing Commands

```bash
# Complete unit test suite (fast, no API calls)
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/ -v --cov=pm_ai --cov-report=html

# Integration tests with API call limits
PM_AI_TEST_MODE=integration PM_AI_MAX_API_CALLS_PER_TEST_RUN=50 pytest pm_ai/tests/integration/ -v

# End-to-end tests (full system validation)
PM_AI_TEST_MODE=e2e pytest pm_ai/tests/e2e/ -v

# All tests with coverage and performance tracking
pytest pm_ai/tests/ --cov=pm_ai --cov-report=html --durations=10

# Fast development testing (unit tests only)
PM_AI_TEST_MODE=unit pytest pm_ai/tests/unit/ -x -v

# Cost-controlled integration testing
PM_AI_TEST_MODE=integration PM_AI_MAX_API_CALLS_PER_TEST_RUN=25 pytest pm_ai/tests/integration/ -v --tb=short
```

### Final Acceptance Criteria for Phase 1

#### Overall System Requirements
- [ ] **Natural Language Understanding**: System processes queries without hardcoded patterns
- [ ] **Conversation Context**: Multi-turn conversations maintain context and reference resolution
- [ ] **Tool Integration**: All existing tools work with new agentic framework
- [ ] **Error Handling**: Graceful handling of API failures, invalid inputs, and edge cases
- [ ] **Performance**: Response time under 5 seconds for typical queries
- [ ] **Test Coverage**: Minimum 85% code coverage across all agentic components

#### Testing Quality Requirements
- [ ] **Unit Test Performance**: Complete unit test suite executes in under 30 seconds
- [ ] **Integration Test Limits**: Maximum 50 OpenAI API calls per complete test run
- [ ] **Test Reliability**: Unit tests 100% pass rate, Integration tests >95% pass rate
- [ ] **Environment Isolation**: Zero cross-contamination between test and production API usage
- [ ] **Mock Accuracy**: Unit test mocks reflect real API behavior with 95% fidelity
- [ ] **Cost Control**: Integration test costs under $5 per complete test run

#### Hybrid Testing Strategy Validation
- [ ] **API Key Separation**: Test and production environments completely isolated
- [ ] **Mock-Reality Alignment**: Unit test mocks accurately represent real API responses
- [ ] **Cost Monitoring**: API call tracking prevents cost overruns in all test scenarios
- [ ] **Test Mode Switching**: Clear separation between unit/integration/e2e test execution
- [ ] **Error Recovery**: Comprehensive error handling for all testing failure scenarios

### Error Recovery for Complete System

**API Key Issues**: Verify .env and .env.test separation, check PM_AI_TEST_MODE settings
**Integration Test Failures**: Reduce API call limits, verify OpenAI service availability
**Unit Test Mock Issues**: Update mock responses to match real API format changes
**Performance Issues**: Use unit tests during development, integration tests for validation
**Cost Overruns**: Implement strict API call limits, use cached responses for repeated tests
**Environment Conflicts**: Clear separation of test/production configurations

---

## Task 5: Create Enhanced Agent Framework

**Objective**: Replace the hardcoded rule-based agent system with an LLM-powered agentic framework that integrates all the new components.

**Files to Modify**:
- `pm_ai/agents/__init__.py` (complete replacement)
- `pm_ai/agents/property_manager.py` (update agent definition)

**Dependencies**: Tasks 1-4 (All previous components)

**Estimated Time**: 6 hours

### Implementation Details

Replace the existing rule-based agent with an LLM-powered version:

```python
# pm_ai/agents/__init__.py (COMPLETE REPLACEMENT)
"""
Enhanced agentic framework with LLM-powered intelligence.
Replaces hardcoded pattern matching with natural language understanding.
"""
import os
import logging
from typing import Dict, List, Callable, Optional, Any, TypedDict

from ..intelligence.llm_engine import LLMEngine, LLMConfig
from ..intelligence.intent_parser import IntentParser
from ..intelligence.context_manager import ContextManager
from ..conversation.session_manager import SessionManager
from ..conversation.memory_store import MemoryStore
from ..integration.tool_orchestrator import ToolOrchestrator
from ..integration.result_synthesizer import ResultSynthesizer

class CompletionResult(TypedDict):
    """Result from agent completion."""
    final_output: str
    session_id: str
    tools_used: List[str]
    confidence: float

class AgenticAgent:
    """
    Enhanced agentic framework with LLM-powered intelligence.

    Replaces rule-based pattern matching with:
    - Natural language understanding via GPT-4
    - Conversation context and memory
    - Intelligent tool selection and orchestration
    - Autonomous reasoning and response generation
    """

    def __init__(self, name: str, instructions: str, tools: List[Callable]):
        self.name = name
        self.instructions = instructions
        self.tools = tools

        # Initialize core components
        self.llm_engine = LLMEngine(LLMConfig())
        self.intent_parser = IntentParser(self.llm_engine)
        self.memory_store = MemoryStore()
        self.session_manager = SessionManager(self.memory_store)
        self.tool_orchestrator = ToolOrchestrator(self.llm_engine)
        self.result_synthesizer = ResultSynthesizer(self.llm_engine)

        # Register tools with orchestrator
        self._register_tools()

        self.logger = logging.getLogger(__name__)

    def _register_tools(self):
        """Register all available tools with the orchestrator."""
        for tool in self.tools:
            tool_name = tool.__name__
            tool_doc = tool.__doc__ or f"Tool function: {tool_name}"
            self.tool_orchestrator.register_tool(tool_name, tool, tool_doc)

    def get_tool_by_name(self, tool_name: str) -> Optional[Callable]:
        """Get tool by name (for backward compatibility)."""
        return self.tool_orchestrator.available_tools.get(tool_name)

class AgenticRunner:
    """
    Enhanced runner for agentic interactions.

    Processes queries through the complete agentic pipeline:
    1. Parse intent from natural language
    2. Maintain conversation context
    3. Select and execute appropriate tools
    4. Synthesize intelligent responses
    """

    @staticmethod
    def run_sync(agent: AgenticAgent, query: str, session_id: Optional[str] = None) -> CompletionResult:
        """
        Process query through the agentic pipeline.

        Args:
            agent: AgenticAgent instance
            query: User's natural language query
            session_id: Optional session ID for conversation continuity

        Returns:
            CompletionResult with response and metadata
        """
        try:
            # Get or create session
            if not session_id:
                session_id = agent.session_manager.create_session()

            # Get conversation context
            context = agent.session_manager.get_context(session_id)

            # Parse user intent
            parsed_intent = agent.intent_parser.parse_query(query, context)

            # Check if clarification is needed
            if parsed_intent.clarification_needed:
                return CompletionResult(
                    final_output=parsed_intent.clarification_needed,
                    session_id=session_id,
                    tools_used=[],
                    confidence=parsed_intent.confidence
                )

            # Execute appropriate tools
            tool_results = agent.tool_orchestrator.execute_intent(parsed_intent, context)

            # Synthesize response
            response = agent.result_synthesizer.synthesize_response(
                query=query,
                parsed_intent=parsed_intent,
                tool_results=tool_results,
                context=context
            )

            # Update conversation memory
            agent.session_manager.add_turn(
                session_id=session_id,
                user_query=query,
                parsed_intent=parsed_intent.__dict__,
                system_response=response,
                tools_used=[r.tool_name for r in tool_results]
            )

            return CompletionResult(
                final_output=response,
                session_id=session_id,
                tools_used=[r.tool_name for r in tool_results],
                confidence=parsed_intent.confidence
            )

        except Exception as e:
            agent.logger.exception(f"Error processing query: {query}")
            return CompletionResult(
                final_output=f"I encountered an error processing your request: {str(e)}",
                session_id=session_id or "error",
                tools_used=[],
                confidence=0.0
            )

# Backward compatibility aliases
Agent = AgenticAgent
Runner = AgenticRunner
```

### Acceptance Criteria
- [ ] AgenticAgent initializes all components correctly
- [ ] Tool registration works with existing tool functions
- [ ] AgenticRunner processes queries through complete pipeline
- [ ] Error handling prevents system crashes
- [ ] Backward compatibility maintained for existing code
- [ ] Session management integrates properly
- [ ] Response synthesis produces coherent outputs

---

## Task 6: Update Main Application Entry Point

**Objective**: Update the main application to use the new agentic framework while maintaining the same CLI interface.

**Files to Modify**: `pm_ai/main.py`

**Dependencies**: Task 5 (Enhanced Agent Framework)

**Estimated Time**: 2 hours

### Implementation Details

Update the main application to use the new agentic system:

```python
# pm_ai/main.py (MODIFICATIONS)
#!/usr/bin/env python
"""
PM_AI - Property Manager AI Assistant (Enhanced Agentic Version)

This is the main entry point for the Property Manager AI application.
Now powered by LLM-based natural language understanding.
"""
import os
from dotenv import load_dotenv
import sys
import os
import logging

# Add the project root to the path so we can use absolute imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from pm_ai.agents import AgenticRunner as Runner
from pm_ai.agents.property_manager import create_agent

def setup_logging():
    """Configure logging for the application."""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('pm_ai.log'),
            logging.StreamHandler()
        ]
    )

def main():
    """
    Run the Property Manager AI assistant with interactive prompts.
    Now with enhanced agentic capabilities.
    """
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)

    # Load environment variables
    load_dotenv()

    # Check for required environment variables
    if not os.getenv("OPENAI_API_KEY"):
        print("Error: OPENAI_API_KEY not found in environment variables.")
        print("Please add it to your .env file or export it directly.")
        print("Example: export OPENAI_API_KEY='your-api-key-here'")
        return

    # Create the enhanced agentic agent
    try:
        agent = create_agent()
        logger.info("Agentic Property Manager AI initialized successfully")
    except Exception as e:
        print(f"Error initializing agent: {e}")
        logger.error(f"Agent initialization failed: {e}")
        return

    # Welcome message
    print("\n" + "="*70)
    print("Welcome to Property Manager AI - Enhanced Agentic Assistant")
    print("="*70)
    print("I can now understand natural language and maintain conversation context!")
    print("\nExample questions you can ask:")
    print("- How's my beachfront property doing lately?")
    print("- Compare P123 to similar properties in the area")
    print("- What improvements would you recommend for the downtown rental?")
    print("- Show me all properties in the luxury cohort")
    print("- Is there anything unusual about P456's performance?")
    print("- How did my properties perform last quarter?")
    print("\nType 'exit' or 'quit' to end the session.")
    print("="*70 + "\n")

    # Initialize session
    session_id = None

    # Interactive loop
    while True:
        # Get user input
        try:
            user_input = input("\nYou: ").strip()
        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break

        # Check for exit command
        if user_input.lower() in ["exit", "quit", "bye", "goodbye"]:
            print("\nThank you for using Property Manager AI. Goodbye!")
            break

        if not user_input:
            continue

        # Process the query with enhanced agentic capabilities
        print("\nProperty Manager AI: ", end="", flush=True)
        try:
            result = Runner.run_sync(agent, user_input, session_id)

            # Update session ID for conversation continuity
            session_id = result["session_id"]

            # Display response
            print(result["final_output"])

            # Show confidence and tools used (optional debug info)
            if os.getenv("PM_AI_DEBUG", "").lower() == "true":
                print(f"\n[Debug] Confidence: {result['confidence']:.2f}, Tools: {result['tools_used']}")

        except Exception as e:
            logger.exception("Error processing user query")
            print(f"Sorry, I encountered an error: {e}")
            print("Please try rephrasing your question or contact support if the issue persists.")

if __name__ == "__main__":
    main()
```

### Acceptance Criteria
- [ ] Main application starts with new agentic framework
- [ ] CLI interface remains familiar to users
- [ ] Session continuity works across conversation turns
- [ ] Error handling provides helpful messages
- [ ] Debug mode shows confidence and tools used
- [ ] Logging captures important events
- [ ] Environment variable validation works

### Testing Commands
```bash
# Test the enhanced application
cd /path/to/project
python -m pm_ai.main

# Test with debug mode
PM_AI_DEBUG=true python -m pm_ai.main

# Test environment validation
unset OPENAI_API_KEY
python -m pm_ai.main  # Should show error message
```

---

## Task 7: Create Comprehensive Test Suite

**Objective**: Create tests for all new components to ensure reliability and catch regressions.

**Files to Create**:
- `pm_ai/tests/test_llm_engine.py`
- `pm_ai/tests/test_intent_parser.py`
- `pm_ai/tests/test_agentic_framework.py`

**Dependencies**: All previous tasks

**Estimated Time**: 4 hours

### Implementation Details

Create comprehensive tests for the new agentic system:

```python
# pm_ai/tests/test_agentic_framework.py
"""
Comprehensive tests for the enhanced agentic framework.
"""
import pytest
import os
from unittest.mock import Mock, patch

from pm_ai.agents import AgenticAgent, AgenticRunner
from pm_ai.intelligence.intent_parser import QueryIntent
from pm_ai.tools.property_metrics import get_property_metrics
from pm_ai.tools.property_comparisons import get_property_comparisons

class TestAgenticFramework:
    """Test the complete agentic framework integration."""

    @pytest.fixture
    def mock_agent(self):
        """Create a mock agent for testing."""
        tools = [get_property_metrics, get_property_comparisons]
        agent = AgenticAgent(
            name="TestAgent",
            instructions="Test agent for property management",
            tools=tools
        )
        return agent

    @patch.dict(os.environ, {'OPENAI_API_KEY': 'test-key'})
    def test_agent_initialization(self, mock_agent):
        """Test that agent initializes correctly."""
        assert mock_agent.name == "TestAgent"
        assert len(mock_agent.tools) == 2
        assert mock_agent.llm_engine is not None
        assert mock_agent.intent_parser is not None
        assert mock_agent.tool_orchestrator is not None

    @patch('pm_ai.intelligence.llm_engine.OpenAI')
    def test_natural_language_query_processing(self, mock_openai, mock_agent):
        """Test processing of natural language queries."""
        # Mock OpenAI response for intent parsing
        mock_response = Mock()
        mock_response.choices = [Mock()]
        mock_response.choices[0].message.function_call.name = "extract_intent"
        mock_response.choices[0].message.function_call.arguments = '''
        {
            "intent": "property_performance",
            "confidence": 0.95,
            "parameters": {
                "property_ids": ["P123"],
                "time_period": "last_30_days"
            }
        }
        '''
        mock_response.usage.prompt_tokens = 100
        mock_response.usage.completion_tokens = 50
        mock_response.usage.total_tokens = 150

        mock_openai.return_value.chat.completions.create.return_value = mock_response

        # Test query processing
        result = AgenticRunner.run_sync(
            mock_agent,
            "How is my property P123 performing this month?"
        )

        assert "final_output" in result
        assert result["confidence"] > 0.8
        assert len(result["tools_used"]) > 0

    def test_conversation_continuity(self, mock_agent):
        """Test that conversation context is maintained."""
        # First query
        result1 = AgenticRunner.run_sync(mock_agent, "How is P123 doing?")
        session_id = result1["session_id"]

        # Follow-up query using same session
        result2 = AgenticRunner.run_sync(
            mock_agent,
            "What about compared to similar properties?",
            session_id
        )

        assert result2["session_id"] == session_id
        # Context should help resolve "similar properties" reference

    def test_error_handling(self, mock_agent):
        """Test error handling for various failure scenarios."""
        # Test with invalid query
        result = AgenticRunner.run_sync(mock_agent, "")
        assert "error" in result["final_output"].lower() or "help" in result["final_output"].lower()

        # Test with malformed input
        result = AgenticRunner.run_sync(mock_agent, "!@#$%^&*()")
        assert result["confidence"] < 0.8  # Should have low confidence

    def test_tool_integration(self, mock_agent):
        """Test that existing tools work with new framework."""
        # Verify tools are registered
        assert "get_property_metrics" in mock_agent.tool_orchestrator.available_tools
        assert "get_property_comparisons" in mock_agent.tool_orchestrator.available_tools

        # Test tool execution
        tool = mock_agent.get_tool_by_name("get_property_metrics")
        assert tool is not None

        # Test tool result
        result = tool("P123", "last_30_days")
        assert isinstance(result, dict)

if __name__ == "__main__":
    pytest.main([__file__])
```

### Acceptance Criteria
- [ ] All tests pass successfully
- [ ] Agent initialization is tested
- [ ] Natural language processing is verified
- [ ] Conversation continuity is validated
- [ ] Error handling scenarios are covered
- [ ] Tool integration works correctly
- [ ] Mock objects prevent actual API calls during testing

### Testing Commands
```bash
# Run all tests
cd /path/to/project
python -m pytest pm_ai/tests/ -v

# Run specific test file
python -m pytest pm_ai/tests/test_agentic_framework.py -v

# Run with coverage
python -m pytest pm_ai/tests/ --cov=pm_ai --cov-report=html
```

---

## Final Integration and Validation

### Complete System Test

After implementing all tasks, perform this comprehensive system test:

```bash
# 1. Install dependencies
poetry install

# 2. Set environment variables
export OPENAI_API_KEY="your-api-key-here"
export PM_AI_DEBUG="true"

# 3. Run the enhanced application
python -m pm_ai.main

# 4. Test natural language queries:
# - "How's my beachfront property doing lately?"
# - "Compare P123 to similar properties"
# - "Any recommendations for improving revenue?"
# - "What's unusual about the downtown properties?"
# - "Show me the luxury cohort performance"

# 5. Test conversation continuity:
# - Ask about a property, then ask "What about last quarter?"
# - Reference previous topics: "How does that compare to P456?"

# 6. Run test suite
python -m pytest pm_ai/tests/ -v
```

### Success Criteria for Phase 1

- [ ] **Natural Language Understanding**: System understands queries without exact patterns
- [ ] **Conversation Context**: Follow-up questions work with context from previous turns
- [ ] **Tool Integration**: All existing tools work with new framework
- [ ] **Error Handling**: Graceful handling of API failures and invalid inputs
- [ ] **Performance**: Response time under 5 seconds for typical queries
- [ ] **Backward Compatibility**: Existing functionality preserved
- [ ] **Test Coverage**: All critical paths covered by automated tests

### Common Issues and Solutions

**Issue**: OpenAI API rate limits
**Solution**: Implement exponential backoff in LLMEngine retry logic

**Issue**: Context memory growing too large
**Solution**: Implement context pruning in MemoryStore cleanup

**Issue**: Tool parameter extraction failures
**Solution**: Add validation and fallback parameter defaults

**Issue**: Session management memory leaks
**Solution**: Verify session cleanup in MemoryStore works correctly

---

## Next Steps (Phase 2 Preview)

After Phase 1 completion, Phase 2 will focus on:
- Advanced multi-step reasoning chains
- Proactive insight generation
- Enhanced context-aware clarification
- Performance optimization and caching
- Advanced error recovery mechanisms

This completes the Phase 1 implementation guide. All tasks are designed to be executed independently by engineers with the provided context and specifications.
