# Migration Guide

This document provides guidance for migrating from the original flat project structure to the new `pm_ai` package structure.

## Structure Changes

### Old Structure:
```
/
├── agent-tool           # Main agent script with no extension
├── db_utils.py          # Database utility functions
├── db_setup.py          # Database setup script
├── scripts/
│   └── test_agent_db.py # Test script
├── README.md            # Documentation
└── .env                 # Environment variables
```

### New Structure:
```
pm_ai/
├── agents/
│   └── finance_buddy.py   # Agent implementation (renamed from agent-tool)
├── config/
│   └── settings.py        # Centralized configuration
├── data/
│   └── transactions.db    # SQLite database
├── db/
│   ├── setup.py           # Database setup (renamed from db_setup.py)
│   └── utils.py           # Database utilities (renamed from db_utils.py)
├── docs/
│   └── ERROR_LOG.md       # Documentation on errors and solutions
├── tests/
│   └── test_agent.py      # Test script (renamed from test_agent_db.py)
├── tools/
│   ├── exchange_rate.py   # Exchange rate tool function
│   └── spending_query.py  # Spending query tool function
├── main.py                # Main entry point
└── README.md              # Documentation
```

## Import Changes

### Old Imports:
```python
# Executing hyphenated file
with open('agent-tool') as f:
    exec(f.read())

# Direct imports
from db_utils import get_spending_data
```

### New Imports:
```python
# Proper package imports
from pm_ai.agents.finance_buddy import agent, create_agent
from pm_ai.tools.exchange_rate import get_exchange_rate
from pm_ai.tools.spending_query import get_spending_by_period
from pm_ai.db.utils import get_spending_data, parse_period
from pm_ai.config.settings import DB_PATH
```

## Configuration Changes

### Old Configuration:
- Environment variables loaded in multiple places with `load_dotenv()`
- Paths constructed relative to running script

### New Configuration:
- Centralized in `pm_ai/config/settings.py`
- Consistent path handling using Python's `pathlib`

## Running the Application

### Old Method:
```bash
# Run the agent directly
python agent-tool

# Run tests
python scripts/test_agent_db.py
```

### New Method:
```bash
# Run the application
python -m pm_ai.main

# Run tests
python -m pm_ai.tests.test_agent

# Initialize database
python -m pm_ai.db.setup
```

## Common Migration Issues

1. **Import Errors**: If you see `ModuleNotFoundError`, make sure that:
   - Your `PYTHONPATH` includes the project root
   - You're using the new module paths

2. **Database Path**: The default database path is now:
   ```
   pm_ai/data/transactions.db
   ```
   Update your `.env` file accordingly.

3. **API Keys**: Make sure to transfer your API keys from the old `.env` file to the new one in the `pm_ai` directory.

## Benefits of the New Structure

1. **Maintainability**: Clearly separated modules with single responsibilities
2. **Scalability**: Easy to add new tools and features
3. **Testability**: Dedicated test directory with proper imports
4. **Documentation**: Organized docs folder for all project documentation
