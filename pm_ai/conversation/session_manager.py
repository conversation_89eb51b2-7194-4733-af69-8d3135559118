"""
Session management and context tracking for Property Manager AI.
"""
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
import json

from .memory_store import MemoryStore, ConversationTurn

class SessionManager:
    """
    Manages conversation sessions and provides context-aware utilities.
    
    Responsibilities:
    - Track active conversation sessions
    - Maintain and update context
    - Provide conversation history for context window
    - Handle reference resolution for property mentions
    """
    
    def __init__(self, memory_store: Optional[MemoryStore] = None):
        self.memory_store = memory_store or MemoryStore()
        self.active_session_id = None
        self.session_timeout = timedelta(hours=1)
    
    def start_session(self, user_id: Optional[str] = None) -> str:
        """Start a new conversation session."""
        session_id = self.memory_store.create_session(user_id)
        self.active_session_id = session_id
        return session_id
    
    def get_or_create_session(self, session_id: Optional[str] = None) -> str:
        """Get existing session or create new one."""
        if session_id:
            session = self.memory_store.get_session(session_id)
            if session:
                # Check if session is expired
                if datetime.now() - session.last_activity > self.session_timeout:
                    # Create new session if expired
                    return self.start_session(session.user_id)
                else:
                    self.active_session_id = session_id
                    return session_id
        
        # No valid session, create new one
        return self.start_session()
    
    def add_interaction(
        self,
        user_query: str,
        parsed_intent: Dict[str, Any],
        system_response: str,
        tools_used: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """
        Add a complete interaction (user query + system response) to the current session.
        
        Args:
            user_query: The user's input query
            parsed_intent: The parsed intent from the query
            system_response: The final response from the system
            tools_used: List of tools used to generate the response
            metadata: Additional metadata about the interaction
            
        Returns:
            The ID of the newly created turn
        """
        if not self.active_session_id:
            self.active_session_id = self.start_session()
        
        return self.memory_store.add_turn(
            self.active_session_id,
            user_query,
            parsed_intent,
            system_response,
            tools_used,
            metadata
        )
    
    def build_conversation_context(self, max_turns: int = 5) -> Dict[str, Any]:
        """
        Build conversation context for LLM prompt engineering.
        
        Args:
            max_turns: Maximum number of previous turns to include
            
        Returns:
            Dictionary with conversation context
        """
        if not self.active_session_id:
            return {"history": [], "references": {}, "preferences": {}}
        
        session = self.memory_store.get_session(self.active_session_id)
        if not session:
            return {"history": [], "references": {}, "preferences": {}}
        
        # Get recent conversation history
        recent_turns = self.memory_store.get_recent_turns(
            self.active_session_id, 
            count=max_turns
        )
        
        # Extract property and cohort references
        references = self._extract_references(recent_turns)
        
        # Build context dictionary
        context = {
            "history": [
                {
                    "user": turn.user_query,
                    "system": turn.system_response,
                    "timestamp": turn.timestamp.isoformat()
                }
                for turn in recent_turns
            ],
            "references": references,
            "preferences": session.preferences
        }
        
        # Add any existing context from the session
        if session.context:
            context.update(session.context)
        
        return context
    
    def update_context(self, context_updates: Dict[str, Any]):
        """
        Update session context with new information.
        
        Args:
            context_updates: Dictionary of context information to update
        """
        if not self.active_session_id:
            return
        
        self.memory_store.update_context(
            self.active_session_id,
            context_updates
        )
    
    def _extract_references(self, turns: List[ConversationTurn]) -> Dict[str, Any]:
        """
        Extract references to properties, cohorts, etc. from conversation turns.
        
        Args:
            turns: List of conversation turns
            
        Returns:
            Dictionary of references extracted from conversation
        """
        references = {
            "properties": {},      # Property IDs mentioned
            "cohorts": set(),      # Cohorts mentioned
            "time_periods": {},    # Time periods mentioned
            "metrics": set()       # Metrics mentioned
        }
        
        # Process turns from oldest to newest
        for turn in turns:
            # Skip turns without parsed intent
            if not turn.parsed_intent:
                continue
                
            # Extract parameters from intent
            params = turn.parsed_intent.get("parameters", {})
            
            # Property IDs
            if "property_ids" in params:
                for prop_id in params["property_ids"]:
                    references["properties"][prop_id] = turn.timestamp.isoformat()
            
            # Cohort
            if "cohort" in params:
                references["cohorts"].add(params["cohort"])
            
            # Time period
            if "time_period" in params:
                references["time_periods"][params["time_period"]] = turn.timestamp.isoformat()
            
            # Metrics
            if "metrics" in params:
                references["metrics"].update(params["metrics"])
        
        # Convert sets to lists for JSON serialization
        references["cohorts"] = list(references["cohorts"])
        references["metrics"] = list(references["metrics"])
        
        return references
