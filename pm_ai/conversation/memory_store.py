"""
In-memory conversation storage for maintaining context across turns.
"""
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from datetime import datetime
import uuid

@dataclass
class ConversationTurn:
    """Single turn in a conversation."""
    turn_id: str
    timestamp: datetime
    user_query: str
    parsed_intent: Dict[str, Any]
    system_response: str
    tools_used: List[str] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)

@dataclass
class ConversationSession:
    """Complete conversation session with multiple turns."""
    session_id: str
    user_id: Optional[str]
    start_time: datetime
    last_activity: datetime
    turns: List[ConversationTurn] = field(default_factory=list)
    context: Dict[str, Any] = field(default_factory=dict)
    preferences: Dict[str, Any] = field(default_factory=dict)

class MemoryStore:
    """
    In-memory storage for conversation sessions and context.
    
    In production, this would be replaced with persistent storage
    (Redis, PostgreSQL, etc.) but for Phase 1, in-memory is sufficient.
    """
    
    def __init__(self):
        self.sessions: Dict[str, ConversationSession] = {}
        self.max_sessions = 100  # Prevent memory bloat
        self.max_turns_per_session = 50
    
    def create_session(self, user_id: Optional[str] = None) -> str:
        """Create new conversation session."""
        session_id = str(uuid.uuid4())
        now = datetime.now()
        
        session = ConversationSession(
            session_id=session_id,
            user_id=user_id,
            start_time=now,
            last_activity=now
        )
        
        self.sessions[session_id] = session
        self._cleanup_old_sessions()
        
        return session_id
    
    def get_session(self, session_id: str) -> Optional[ConversationSession]:
        """Retrieve conversation session."""
        return self.sessions.get(session_id)
    
    def add_turn(
        self,
        session_id: str,
        user_query: str,
        parsed_intent: Dict[str, Any],
        system_response: str,
        tools_used: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Add new turn to conversation session."""
        session = self.sessions.get(session_id)
        if not session:
            raise ValueError(f"Session {session_id} not found")
        
        turn_id = str(uuid.uuid4())
        turn = ConversationTurn(
            turn_id=turn_id,
            timestamp=datetime.now(),
            user_query=user_query,
            parsed_intent=parsed_intent,
            system_response=system_response,
            tools_used=tools_used or [],
            metadata=metadata or {}
        )
        
        session.turns.append(turn)
        session.last_activity = datetime.now()
        
        # Limit turns per session
        if len(session.turns) > self.max_turns_per_session:
            session.turns = session.turns[-self.max_turns_per_session:]
        
        return turn_id
    
    def update_context(self, session_id: str, context_updates: Dict[str, Any]):
        """Update session context."""
        session = self.sessions.get(session_id)
        if session:
            session.context.update(context_updates)
            session.last_activity = datetime.now()
    
    def get_recent_turns(self, session_id: str, count: int = 5) -> List[ConversationTurn]:
        """Get recent conversation turns."""
        session = self.sessions.get(session_id)
        if not session:
            return []
        
        return session.turns[-count:] if session.turns else []
    
    def _cleanup_old_sessions(self):
        """Remove oldest sessions if limit exceeded."""
        if len(self.sessions) > self.max_sessions:
            # Sort by last activity and remove oldest
            sorted_sessions = sorted(
                self.sessions.items(),
                key=lambda x: x[1].last_activity
            )
            
            sessions_to_remove = len(self.sessions) - self.max_sessions
            for session_id, _ in sorted_sessions[:sessions_to_remove]:
                del self.sessions[session_id]
