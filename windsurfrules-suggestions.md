# Suggested Windsurf Rules

Based on our experience implementing the Database Query Tool for Financial Analysis, here are three suggested rules that could improve future development:

## 1. Error Documentation Rule

```markdown
---
description: Automatically document errors in ERROR_LOG.md during implementation
author: cascade
version: 1.0
tags: ["development", "documentation", "errors", "learning"]
globs: ["*.py", "*.md"]
---
# Error Documentation

When encountering errors while implementing tasks from TASKS.md:

1. Document the error, cause, and solution in ERROR_LOG.md following the established format
2. For framework-specific errors, include code examples of both incorrect and correct usage
3. Tag errors with relevant categories for future searchability
4. Always check ERROR_LOG.md before implementing similar functionality
```

## 2. Special Filename Handling Rule

```markdown
---
description: Handle non-standard filenames and import patterns
author: cascade
version: 1.0
tags: ["development", "python", "imports"]
globs: ["*.py"]
---
# Non-Standard Filename Handling

When working with files that have non-standard naming conventions:

1. For Python files with hyphens, document import techniques in ERROR_LOG.md
2. Avoid creating new files with names that violate language conventions
3. When testing scripts, prefer importlib techniques for hyphenated filenames
4. Consider renaming non-standard files when appropriate
```

## 3. Framework-Aware Testing Rule

```markdown
---
description: Ensure tests respect framework-specific function behaviors
author: cascade
version: 1.0
tags: ["testing", "frameworks", "python"]
globs: ["*test*.py"]
---
# Framework-Aware Testing

When testing functions that use special decorators or frameworks:

1. Understand how decorators (like @function_tool) modify function behavior
2. Test decorated functions within their intended framework context
3. For OpenAI Agent tools, test through the Agent/Runner interface rather than direct calls
4. Document framework-specific testing patterns in ERROR_LOG.md
```

These rules formalize the learning process from our current implementation and ensure that future development benefits from our experiences.
