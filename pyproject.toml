[project]
name = "chat"
version = "0.1.0"
description = ""
authors = [
    {name = "jachian22",email = "<EMAIL>"}
]
readme = "README.md"
requires-python = ">=3.13"
dependencies = [
    "openai (>=1.88.0,<2.0.0)",
    "openai-agents (>=0.0.19,<0.0.20)",
    "python-dotenv (>=1.1.0,<2.0.0)",
    "requests (>=2.31.0,<3.0.0)"
]


[tool.poetry]
packages = [
    { include = "pm_ai" }
]

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"
