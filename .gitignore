# ==== Python artefacts ====
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# ==== Virtual envs & IDE ====
.env
.venv/
env/
venv/
ENV/
env.bak/
venv.bak/
.idea/
.vscode/
*.swp
*.swo

# ==== Secrets ====
.env
pm_ai/.env
*.pem
*.key

# ==== Test / coverage ====
.pytest_cache/
.coverage
htmlcov/
.tox/
.nox/
.hypothesis/
.pytest_cache/
coverage.xml
*.cover

# ==== Data ====
*.db
*.sqlite
*.sqlite3
pm_ai/data/*.db

# ==== OS specific ====
.DS_Store
Thumbs.db
