# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Property Manager AI is an MVP terminal-based chatbot that assists vacation rental property managers and owners. It provides property performance analysis, comparisons, recommendations, and outlier detection using static JSON data files for demonstration purposes.

## Architecture
- **Main Entry Point**: `pm_ai/main.py` - CLI interface for the Property Manager AI
- **Core Components**:
  - `pm_ai/agents/__init__.py` - Contains the Agent and Runner classes with rule-based query handling
  - `pm_ai/agents/property_manager.py` - Property Manager AI agent definition with instructions
  - `pm_ai/tools/*.py` - Tool functions for fetching property data
  - `pm_ai/data/*.json` - Static JSON data files with property information
- **Core Dependencies**:
  - `openai` - For potential future LLM integration
  - `python-dotenv` - Environment variable management
- **Documentation**: `pm_ai/docs/ERROR_LOG.md` - Error tracking and learnings

## Environment Setup

The project requires these environment variables in `.env`:
- `OPENAI_API_KEY` - OpenAI API access (for future LLM integration)

## Common Commands

**Install dependencies:**
```bash
poetry install
```

**Run the application:**
```bash
python agent-tool
```

**Make agent-tool executable (optional):**
```bash
chmod +x agent-tool
./agent-tool
```

## Code Structure

The application follows a simple single-file architecture:
- `get_exchange_rate()` - Tool function that fetches currency rates from ExchangeRate-API
- `assistant` - Agent instance configured with the exchange rate tool
- Main execution uses `Runner.run_sync()` to process queries

## Development Notes

- The project uses Poetry for dependency management (pyproject.toml)
- Main code file lacks `.py` extension but contains standard Python code
- API keys are loaded from `.env` file and should never be committed
- The `commands/issues.md` file contains a template for creating GitHub issues using AI

## Error Tracking and Continuous Learning

1. **Check ERROR_LOG.md Before Implementation**: Before implementing any new task from TASKS.md, review the ERROR_LOG.md file for relevant errors and learnings from previous implementations.

2. **Document New Errors**: For each new implementation task (like "Implementing Database Query Tool for Financial Analysis"), document any errors encountered in ERROR_LOG.md under a clearly labeled section.

3. **Error Documentation Format**:
   ```markdown
   ### [Task Name]
   <learnings>
   1. **[Error Category]**
      - **Error**: [Actual error message]
      - **Cause**: [What caused the error]
      - **Solution**: [How it was resolved]
      - **Code Example**: [Example of working solution]
   </learnings>
   ```

4. **Common Error Categories to Watch For**:
   - Module import issues (especially with non-standard filenames)
   - Framework-specific function usage (e.g., decorated functions)
   - Environment variable configuration
   - Test script edge cases
   - Python version compatibility issues

5. **Review Process**: When extending functionality, search ERROR_LOG.md for relevant keywords to identify potential issues before coding.