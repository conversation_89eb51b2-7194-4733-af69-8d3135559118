# Property Manager AI

> **Project Update**: This project has been fully refactored to focus solely on Property Manager AI functionality. The Financial Assistant components have been removed.

Property Manager AI is a Python-based assistant for property managers to analyze vacation rental performance data using the OpenAI Agents SDK.

## Features

- **Property Performance Metrics**: Analyze occupancy rates, average daily rates, and revenue for individual properties.
- **Property Comparisons**: Compare properties against similar properties in their market cohort.
- **Property Recommendations**: Get data-driven recommendations for property improvements.
- **Cohort Analysis**: View properties grouped by location, type, or other classifications.
- **Outlier Detection**: Identify statistical outliers in property performance metrics.

## Setup

### Prerequisites

- Python 3.8 or newer
- OpenAI API key (optional for future LLM integration)

### Installation

1. **Clone the repository**:
   ```bash
   git clone <repository-url>
   cd <repository-directory>
   ```

2. **Install dependencies**:
   ```bash
   cd pm_ai
   pip install -r requirements.txt
   ```

3. **Set up environment variables** (optional for future LLM integration):
   Create a `.env` file in the `pm_ai` directory with the following variable:
   ```
   OPENAI_API_KEY="your-openai-api-key"
   ```

## Usage

### Basic Usage

Run the interactive Property Manager AI agent:

```bash
python -m pm_ai.main
```

Or use it as a Python module in your code:

```python
from pm_ai.agents.property_manager import agent
from agents import Runner

result = Runner.run_sync(agent, "How is property P123 performing this month?")
print(result["final_output"])
```

### Test Script

To test all functionality:

```bash
python -m pm_ai.tests.test_agent
```

### Example Queries

The Property Manager AI can answer questions like:

- "How is property P123 performing this month?"
- "Compare property P456 with similar properties."
- "What recommendations do you have for property P123?"
- "Show me properties in the Downtown cohort."
- "What are the outlier statistics for property P789?"

## Data Files

The Property Manager AI uses JSON data files for storing property information:

| File                     | Description                                      |
|--------------------------|--------------------------------------------------|
| property_metrics.json    | Performance metrics for individual properties    |
| property_comparisons.json| Comparison data between properties               |
| property_recommendations.json | Recommendations for property improvements   |
| property_outliers.json   | Statistical outliers for various property metrics|
| property_cohorts.json    | Groups of related properties by location/type    |

## Time Period Parsing

The agent can understand various time periods:

- Recent periods: "this month", "last quarter", "past year"
- Month names: "January", "February", etc.
- Months with years: "April 2025"
- Quarters: "Q1", "Q2", "Q3", "Q4"
- Years: "2025"

If no time period is specified, the current month is used.

## Environment Variables

| Variable | Description | Default | Required |
|----------|-------------|---------|----------|
| OPENAI_API_KEY | OpenAI API key | None | No (for future LLM integration) |

## Future Improvements

- Integrate real-time property data from PMS systems
- Implement LLM-based intent recognition for more complex queries
- Add dynamic dashboards and data visualizations
- Expand property recommendations with market analysis
- Enable performance forecasting and trend analysis
- Support multiple property portfolios and user accounts

## Project Refactoring

This project has undergone a major refactoring from its original structure:

- **New Package Structure**: Organized as a proper Python package under `pm_ai/`
- **Renamed**: Changed from "FinanceBuddy" to "PM_AI"
- **Improved Code Organization**: Separated concerns into dedicated modules
- **Better Testing**: Dedicated test directory with proper package imports
- **Documentation**: Added migration guide and error tracking

Refer to the [Migration Guide](pm_ai/docs/MIGRATION_GUIDE.md) for details about the transition.

## Error Tracking

We've implemented a comprehensive error tracking system in this project. Common errors and solutions are documented in [ERROR_LOG.md](pm_ai/docs/ERROR_LOG.md) to help identify and resolve issues quickly.
